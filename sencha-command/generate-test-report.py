#!/usr/bin/env python3
"""
Simple test report generator for JUnit XML results
"""
import os
import xml.etree.ElementTree as ET
from datetime import datetime
import glob

def parse_test_results(results_dir):
    """Parse all XML test result files and extract summary information"""
    test_files = glob.glob(os.path.join(results_dir, "TEST-*.xml"))
    
    total_tests = 0
    total_failures = 0
    total_errors = 0
    total_skipped = 0
    total_time = 0.0
    
    test_suites = []
    
    for test_file in sorted(test_files):
        try:
            tree = ET.parse(test_file)
            root = tree.getroot()
            
            suite_name = root.get('name', 'Unknown')
            tests = int(root.get('tests', 0))
            failures = int(root.get('failures', 0))
            errors = int(root.get('errors', 0))
            skipped = int(root.get('skipped', 0))
            time = float(root.get('time', 0.0))
            
            total_tests += tests
            total_failures += failures
            total_errors += errors
            total_skipped += skipped
            total_time += time
            
            # Get individual test cases
            test_cases = []
            for testcase in root.findall('testcase'):
                case_name = testcase.get('name', 'Unknown')
                case_time = float(testcase.get('time', 0.0))
                case_status = 'PASSED'
                case_message = ''
                
                if testcase.find('failure') is not None:
                    case_status = 'FAILED'
                    failure = testcase.find('failure')
                    case_message = failure.get('message', '') if failure is not None else ''
                elif testcase.find('error') is not None:
                    case_status = 'ERROR'
                    error = testcase.find('error')
                    case_message = error.get('message', '') if error is not None else ''
                elif testcase.find('skipped') is not None:
                    case_status = 'SKIPPED'
                
                test_cases.append({
                    'name': case_name,
                    'time': case_time,
                    'status': case_status,
                    'message': case_message
                })
            
            test_suites.append({
                'name': suite_name,
                'tests': tests,
                'failures': failures,
                'errors': errors,
                'skipped': skipped,
                'time': time,
                'test_cases': test_cases
            })
            
        except Exception as e:
            print(f"Error parsing {test_file}: {e}")
    
    return {
        'total_tests': total_tests,
        'total_failures': total_failures,
        'total_errors': total_errors,
        'total_skipped': total_skipped,
        'total_time': total_time,
        'test_suites': test_suites
    }

def generate_html_report(results, output_file):
    """Generate HTML test report"""
    
    # Calculate success rate
    total_run = results['total_tests'] - results['total_skipped']
    success_rate = ((total_run - results['total_failures'] - results['total_errors']) / total_run * 100) if total_run > 0 else 0
    
    html_content = f"""<!DOCTYPE html>
<html>
<head>
    <title>Sencha Command Test Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }}
        .summary {{ display: flex; gap: 20px; margin-bottom: 20px; }}
        .summary-box {{ background-color: #e9ecef; padding: 15px; border-radius: 5px; flex: 1; text-align: center; }}
        .success {{ background-color: #d4edda; color: #155724; }}
        .failure {{ background-color: #f8d7da; color: #721c24; }}
        .error {{ background-color: #fff3cd; color: #856404; }}
        .skipped {{ background-color: #d1ecf1; color: #0c5460; }}
        table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .test-suite {{ margin-bottom: 30px; }}
        .test-suite h3 {{ background-color: #f8f9fa; padding: 10px; margin: 0; border-radius: 5px 5px 0 0; }}
        .status-PASSED {{ color: #28a745; font-weight: bold; }}
        .status-FAILED {{ color: #dc3545; font-weight: bold; }}
        .status-ERROR {{ color: #ffc107; font-weight: bold; }}
        .status-SKIPPED {{ color: #6c757d; font-weight: bold; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Sencha Command Test Report</h1>
        <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="summary">
        <div class="summary-box success">
            <h3>Total Tests</h3>
            <div style="font-size: 24px; font-weight: bold;">{results['total_tests']}</div>
        </div>
        <div class="summary-box {'success' if results['total_failures'] == 0 else 'failure'}">
            <h3>Failures</h3>
            <div style="font-size: 24px; font-weight: bold;">{results['total_failures']}</div>
        </div>
        <div class="summary-box {'success' if results['total_errors'] == 0 else 'error'}">
            <h3>Errors</h3>
            <div style="font-size: 24px; font-weight: bold;">{results['total_errors']}</div>
        </div>
        <div class="summary-box skipped">
            <h3>Skipped</h3>
            <div style="font-size: 24px; font-weight: bold;">{results['total_skipped']}</div>
        </div>
        <div class="summary-box">
            <h3>Success Rate</h3>
            <div style="font-size: 24px; font-weight: bold;">{success_rate:.1f}%</div>
        </div>
        <div class="summary-box">
            <h3>Total Time</h3>
            <div style="font-size: 24px; font-weight: bold;">{results['total_time']:.3f}s</div>
        </div>
    </div>
    
    <h2>Test Suites Summary</h2>
    <table>
        <thead>
            <tr>
                <th>Test Suite</th>
                <th>Tests</th>
                <th>Failures</th>
                <th>Errors</th>
                <th>Skipped</th>
                <th>Time (s)</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
"""
    
    for suite in results['test_suites']:
        status = 'PASSED'
        if suite['failures'] > 0:
            status = 'FAILED'
        elif suite['errors'] > 0:
            status = 'ERROR'
        
        html_content += f"""
            <tr>
                <td>{suite['name']}</td>
                <td>{suite['tests']}</td>
                <td>{suite['failures']}</td>
                <td>{suite['errors']}</td>
                <td>{suite['skipped']}</td>
                <td>{suite['time']:.3f}</td>
                <td class="status-{status}">{status}</td>
            </tr>
"""
    
    html_content += """
        </tbody>
    </table>
    
    <h2>Detailed Test Results</h2>
"""
    
    for suite in results['test_suites']:
        html_content += f"""
    <div class="test-suite">
        <h3>{suite['name']} ({suite['tests']} tests, {suite['time']:.3f}s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>
"""
        
        for test_case in suite['test_cases']:
            html_content += f"""
                <tr>
                    <td>{test_case['name']}</td>
                    <td>{test_case['time']:.3f}</td>
                    <td class="status-{test_case['status']}">{test_case['status']}</td>
                    <td>{test_case['message']}</td>
                </tr>
"""
        
        html_content += """
            </tbody>
        </table>
    </div>
"""
    
    html_content += """
</body>
</html>
"""
    
    with open(output_file, 'w') as f:
        f.write(html_content)

def main():
    results_dir = "build/test/results"
    output_file = "test-report.html"
    
    if not os.path.exists(results_dir):
        print(f"Results directory {results_dir} not found!")
        return
    
    print("Parsing test results...")
    results = parse_test_results(results_dir)
    
    print("Generating HTML report...")
    generate_html_report(results, output_file)
    
    print(f"Test report generated: {output_file}")
    print(f"Total tests: {results['total_tests']}")
    print(f"Failures: {results['total_failures']}")
    print(f"Errors: {results['total_errors']}")
    print(f"Skipped: {results['total_skipped']}")
    print(f"Total time: {results['total_time']:.3f}s")

if __name__ == "__main__":
    main()
