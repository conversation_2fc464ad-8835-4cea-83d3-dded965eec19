<!DOCTYPE html>
<html>
<head>
    <title>Sencha Command Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .summary { display: flex; gap: 20px; margin-bottom: 20px; }
        .summary-box { background-color: #e9ecef; padding: 15px; border-radius: 5px; flex: 1; text-align: center; }
        .success { background-color: #d4edda; color: #155724; }
        .failure { background-color: #f8d7da; color: #721c24; }
        .error { background-color: #fff3cd; color: #856404; }
        .skipped { background-color: #d1ecf1; color: #0c5460; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .test-suite { margin-bottom: 30px; }
        .test-suite h3 { background-color: #f8f9fa; padding: 10px; margin: 0; border-radius: 5px 5px 0 0; }
        .status-PASSED { color: #28a745; font-weight: bold; }
        .status-FAILED { color: #dc3545; font-weight: bold; }
        .status-ERROR { color: #ffc107; font-weight: bold; }
        .status-SKIPPED { color: #6c757d; font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Sencha Command Test Report</h1>
        <p>Generated on: 2025-06-16 15:41:53</p>
    </div>
    
    <div class="summary">
        <div class="summary-box success">
            <h3>Total Tests</h3>
            <div style="font-size: 24px; font-weight: bold;">727</div>
        </div>
        <div class="summary-box failure">
            <h3>Failures</h3>
            <div style="font-size: 24px; font-weight: bold;">4</div>
        </div>
        <div class="summary-box success">
            <h3>Errors</h3>
            <div style="font-size: 24px; font-weight: bold;">0</div>
        </div>
        <div class="summary-box skipped">
            <h3>Skipped</h3>
            <div style="font-size: 24px; font-weight: bold;">2</div>
        </div>
        <div class="summary-box">
            <h3>Success Rate</h3>
            <div style="font-size: 24px; font-weight: bold;">99.4%</div>
        </div>
        <div class="summary-box">
            <h3>Total Time</h3>
            <div style="font-size: 24px; font-weight: bold;">56.218s</div>
        </div>
    </div>
    
    <h2>Test Suites Summary</h2>
    <table>
        <thead>
            <tr>
                <th>Test Suite</th>
                <th>Tests</th>
                <th>Failures</th>
                <th>Errors</th>
                <th>Skipped</th>
                <th>Time (s)</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>

            <tr>
                <td>com.sencha.ant.BaseDispatchTaskTest</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.334</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.ant.GeneratorTaskTest</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>1.431</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.ant.JsPreprocessTaskTest</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.567</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.ant.SenchaCommandTaskTest</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.104</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.cli.CommandsTest</td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.022</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.command.DiagTest</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.080</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.command.SenchaLogManagerTest</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.001</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.command.SenchaTest</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.032</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.command.ant.AntCommandTest</td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.368</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.command.compile.OptimizerCommandTest</td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.298</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.command.filesystem.FileSystemCommandsTest</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.988</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.command.generator.GeneratePackageCommandTest</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>4.002</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.command.generator.WorkspaceCommandTest</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>4.760</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.command.js.JSCommandTest</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.006</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.command.template.TemplateCommandsTest</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>5.064</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.database.DatabaseTest</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.053</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.doxi.DoxiTest</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.107</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.security.CertificateTest</td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>1.422</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.security.CryptorTest</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.007</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.compiler.CompilerContextTest</td>
                <td>49</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>1.470</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.compiler.DeclarationVisitorTest</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.001</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.compiler.DefineRewriterTest</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.002</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.compiler.MarkupCompilerTest</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.009</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.compiler.ast.AstUtilTest</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.004</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.compiler.ast.CodePrinterTest</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.114</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.compiler.ast.ReferenceOptimizerTest</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.008</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.compiler.beautifier.JsBeautifierTest</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.003</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.compiler.jsb.statements.ParserTest</td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.047</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.compiler.meta.NoteTest</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.002</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.compiler.sources.ClassDeclarationVisitorTest</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.011</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.compiler.sources.ES6Test</td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0.529</td>
                <td class="status-FAILED">FAILED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.compiler.sources.MarkupCompileDirectiveProcessorTest</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.003</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.compiler.sources.MarkupDirectiveTest</td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.001</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.compiler.sources.ScriptDirectiveTest</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.001</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.compiler.sources.VariableRenamerTest</td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0.185</td>
                <td class="status-FAILED">FAILED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.generator.DescriptorTest</td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.010</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.generator.GeneratorCommandsTest</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>11.757</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.generator.GeneratorTest</td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.488</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.inspector.InspectorConnectorTest</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.326</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.page.AppJsonBuilderTest</td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.785</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.pkg.BaseRepositoryTest</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.000</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.pkg.EntitlementTest</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>3.159</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.pkg.LocalRepositoryTest</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>2.758</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.pkg.LockerTest</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.219</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.pkg.ModelTest</td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.006</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.slicer.CssDirectiveProcessorTest</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.002</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.slicer.SlicerTest</td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.389</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.splitters.CssFileSplitterTest</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.745</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.upgrade.BuildXmlUpdateTest</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.003</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.tools.upgrade.NpmCompatTransformTest</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.002</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.CharsetDetectorTest</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.002</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.ConfigurationTest</td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.003</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.ConverterTest</td>
                <td>172</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.016</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.DesktopUtilTest</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0.001</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.Diff3Test</td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.003</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.FileUtilTest</td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.016</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.LocatorTest</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.000</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.NameUtilTest</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.034</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.NetworkUtilTest</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0.001</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.ObjectUtilTest</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.000</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.PlatformUtilTest</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.000</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.ReflectionUtilTest</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.097</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.RegexUtilTest</td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.001</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.StringUtilTest</td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.005</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.VersionNameTest</td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.002</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.VersionRangeTest</td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.002</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.VersionTest</td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.001</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.XmlBuilderTest</td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.001</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.XmlParserTest</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.004</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.ZipUtilTest</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.004</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.filters.FilterChainTest</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.001</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.filters.RegexFilterTest</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.001</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.filters.SubstringFilterTest</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.001</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.fs.FsMonitorTest</td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>12.743</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.http.BasicResponderTest</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.309</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

            <tr>
                <td>com.sencha.util.http.ServerTest</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0.285</td>
                <td class="status-PASSED">PASSED</td>
            </tr>

        </tbody>
    </table>
    
    <h2>Detailed Test Results</h2>

    <div class="test-suite">
        <h3>com.sencha.ant.BaseDispatchTaskTest (2 tests, 0.334s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testBaseDispatchTask</td>
                    <td>0.301</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.ant.GeneratorTaskTest (8 tests, 1.431s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testMergeConflict</td>
                    <td>0.515</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>paramTest1</td>
                    <td>0.135</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>paramTest2</td>
                    <td>0.191</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMerge1b</td>
                    <td>0.189</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>paramTest1a</td>
                    <td>0.122</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMergeFauxConflict</td>
                    <td>0.158</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMerge1</td>
                    <td>0.118</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.ant.JsPreprocessTaskTest (2 tests, 0.567s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testPreprocessTask</td>
                    <td>0.567</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.ant.SenchaCommandTaskTest (2 tests, 0.104s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testSenchaCommandTask</td>
                    <td>0.103</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.cli.CommandsTest (37 tests, 0.022s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testConfiguredCommand</td>
                    <td>0.004</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testTooFewArgsWithDefault</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testRequiredArgFromMissingProperty</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testBooleanPropertySetter</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testConfigKeyArgFromPropertyFromConfigKey</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testBooleanPropertySetterNoGetter</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleBooleanPropertySetter</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testAndThenCombination</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testConfiguredCommandWithArg</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDashDashSpacePropertySetter</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testHelpGroupingForSubCmd</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testConfiguresArgFromPropertyWithSwitch</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testSingleAnd</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>requiredArgumentsAreRevalidatedOnEachCommandInstance</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testBasicCommand</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDashSpacePropertySetter</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testRequiredArgFromProperty</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testAndThenCombination2</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testAndThenCombination3</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testConfigKeyArgFromPropertyFromConfigKeyOverride</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDefaultArg</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testTooFewArgs</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testVarArgCommand</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDashDashAssignPropertySetter</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMultiCommandWithDefaults</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testHelpGrouping</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNonExactMatch</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleBooleanPropertySetterWithValue</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testBooleanPropertySetterWithValueNoGetter</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMultipleAnds</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testConfiguresArgFromProperty</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testExactMatch</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDashAssignPropertySetter</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testConfiguresArgSetsProperty</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testPlusPropertySetter</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testAdjacentAnds</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testEnumPropertySetter</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.command.DiagTest (4 tests, 0.080s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testShow</td>
                    <td>0.009</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShowOutput</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testExport</td>
                    <td>0.068</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.command.SenchaLogManagerTest (1 tests, 0.001s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>verifyLoggerName</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.command.SenchaTest (3 tests, 0.032s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testCrashLog</td>
                    <td>0.022</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testCrashLogMultiple</td>
                    <td>0.009</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.command.ant.AntCommandTest (14 tests, 0.368s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>helpRootCmd</td>
                    <td>0.045</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>runExtension2SubCommand</td>
                    <td>0.041</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>helpExtension1SubCmd</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>runRootCmd</td>
                    <td>0.041</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>runExtension1SubCommand2</td>
                    <td>0.041</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>runExtension2SubCommand2</td>
                    <td>0.044</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>helpExtension1Cmd</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>runExtension1SubCommand</td>
                    <td>0.043</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>helpApp</td>
                    <td>0.006</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>runAppSubCmd</td>
                    <td>0.058</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>helpSencha</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>runArgTest</td>
                    <td>0.042</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>helpAppSubCmd</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.command.compile.OptimizerCommandTest (13 tests, 0.298s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>defineWithMixinsRewriterTest</td>
                    <td>0.180</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>defineWithAliasRewriterTest</td>
                    <td>0.011</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>outerParenExpressionRewriterTest</td>
                    <td>0.043</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>defineWithXTypesRewriterTest</td>
                    <td>0.008</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>callExpressionDefineRewriterTest</td>
                    <td>0.008</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>defineWithStaticsRewriterTest</td>
                    <td>0.008</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>simpleRewriterTest</td>
                    <td>0.007</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>defineWithAlternatesRewriterTest</td>
                    <td>0.005</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>callExpressionDefineWithOverrideRewriterTest</td>
                    <td>0.006</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>hexRewriterTest</td>
                    <td>0.006</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>parenExpressionRewriterTest</td>
                    <td>0.007</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>functionRewriterTest</td>
                    <td>0.006</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.command.filesystem.FileSystemCommandsTest (2 tests, 0.988s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>sourceMapTest</td>
                    <td>0.988</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.command.generator.GeneratePackageCommandTest (6 tests, 4.002s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testGeneratePackageWithTheme</td>
                    <td>0.927</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testGeneratePackageWithFramework</td>
                    <td>0.791</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testGenerateThemePackage</td>
                    <td>0.735</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testGenerateSimplePackage</td>
                    <td>0.778</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testGeneratePackageWithFrameworkAndTheme</td>
                    <td>0.770</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.command.generator.WorkspaceCommandTest (5 tests, 4.760s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testPackageExtractionPathOnWorkspace</td>
                    <td>1.569</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testWorkspaceJson</td>
                    <td>0.691</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testPackageGeneration</td>
                    <td>0.973</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testPackageExtractionPathOnApp</td>
                    <td>1.523</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.command.js.JSCommandTest (2 tests, 0.006s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>sandboxTemplateTest</td>
                    <td>0.005</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.command.template.TemplateCommandsTest (6 tests, 5.064s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testCodePackageGeneration</td>
                    <td>0.982</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testTemplatePackageGeneration</td>
                    <td>0.775</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testTemplateInfo</td>
                    <td>0.824</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testTemplateList1</td>
                    <td>0.795</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testTemplateList2</td>
                    <td>0.823</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testTemplateList3</td>
                    <td>0.862</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.database.DatabaseTest (2 tests, 0.053s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>baseDir</td>
                    <td>0.051</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.doxi.DoxiTest (3 tests, 0.107s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testAlternateClassNames</td>
                    <td>0.075</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMethodSignatures</td>
                    <td>0.028</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.security.CertificateTest (14 tests, 1.422s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>mergeSignatures</td>
                    <td>0.371</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>generateCertNoName</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>verifyMsg1</td>
                    <td>0.101</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>hackCert1</td>
                    <td>0.067</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>hackCert2</td>
                    <td>0.109</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>hackCert3</td>
                    <td>0.172</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>generateCertNoEmail</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>storeFile1</td>
                    <td>0.110</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>secretMessage1</td>
                    <td>0.203</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>storeDir1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>storeDir2</td>
                    <td>0.144</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>generateCert1</td>
                    <td>0.067</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>generateCert2</td>
                    <td>0.075</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.security.CryptorTest (3 tests, 0.007s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>encryptTest1</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>encryptTest2</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>encryptTest3</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.compiler.CompilerContextTest (49 tests, 1.470s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testSimpleMixin</td>
                    <td>0.129</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testReferentialScanExtCalls</td>
                    <td>0.018</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testReferentialScanInClassDef</td>
                    <td>0.012</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testXTemplatePrecompiler</td>
                    <td>0.360</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMultiTagFilter</td>
                    <td>0.022</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDeferredOverride</td>
                    <td>0.010</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>includeClassnameTest</td>
                    <td>0.153</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testReferentialScanNestedInClassDef</td>
                    <td>0.011</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testCircularReferenceDetection</td>
                    <td>0.011</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testClosureCompressionUtf8</td>
                    <td>0.206</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFunctionCallDefineForm1</td>
                    <td>0.008</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testJsbOptionsChange</td>
                    <td>0.170</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testBootstrapFilter</td>
                    <td>0.014</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testCompileCommandUnCompressed</td>
                    <td>0.023</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testSimpleDefine</td>
                    <td>0.008</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testOverrideSortOrder</td>
                    <td>0.006</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testReferentialScanNewOperator</td>
                    <td>0.007</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>includeNamespaceTest</td>
                    <td>0.013</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDirectLoad</td>
                    <td>0.003</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testReferentialScanStaticReference</td>
                    <td>0.006</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMetaDefinitionsDefineOnlyCommand</td>
                    <td>0.016</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMultiTagFilter2</td>
                    <td>0.005</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testExcludedOverride</td>
                    <td>0.006</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMetaDefinitionsNoSeperatorCommand</td>
                    <td>0.008</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testReferentialScan</td>
                    <td>0.006</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDefaultsOnItemsConfig</td>
                    <td>0.007</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testCompileCommandYui</td>
                    <td>0.062</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testOptimizerMixinJson</td>
                    <td>0.006</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>excludeAllIncludeTest</td>
                    <td>0.013</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMetaDefinitionsNoTplCommand</td>
                    <td>0.007</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testReservedWordsOptimization</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testSpecialCharForum</td>
                    <td>0.011</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNamespaceFilter</td>
                    <td>0.018</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMetaDefinitionsCommand</td>
                    <td>0.009</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testReferentialScanStaticReferenceOnClassDef</td>
                    <td>0.006</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testExcludedOverrideWithUses</td>
                    <td>0.006</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testReferentialScanXtypeDeepScan</td>
                    <td>0.006</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testOptimizerMixinJsonStrings</td>
                    <td>0.005</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testReferentialScanExtCreateWidget</td>
                    <td>0.007</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testReferentialScanExtCreateWidget2</td>
                    <td>0.006</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testReferentialScanExtCreateWidget3</td>
                    <td>0.006</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>excludeAllTest</td>
                    <td>0.008</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testCallParentOptimizerSuppress</td>
                    <td>0.005</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testReferentialScanLayoutConfigs</td>
                    <td>0.007</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testReferentialScanXtypeDeepScanNestedContainer</td>
                    <td>0.007</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testCommentAssignment</td>
                    <td>0.009</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>commentTest</td>
                    <td>0.012</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNonDeferredOverride</td>
                    <td>0.006</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.compiler.DeclarationVisitorTest (5 tests, 0.001s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testTagMatch</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testTagMatchWithSpaces</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testTailMatch</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testSingleTagMatchWithSpaces</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.compiler.DefineRewriterTest (4 tests, 0.002s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>comparisonTest1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>comparisonTest2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>comparisonTest3</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.compiler.MarkupCompilerTest (2 tests, 0.009s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>compileMarkup</td>
                    <td>0.007</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.compiler.ast.AstUtilTest (3 tests, 0.004s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>walkTest</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>mergeTest</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.compiler.ast.CodePrinterTest (4 tests, 0.114s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testPrinter</td>
                    <td>0.007</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testPrinter2</td>
                    <td>0.102</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testPrinterNonReadable</td>
                    <td>0.004</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.compiler.ast.ReferenceOptimizerTest (3 tests, 0.008s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>optimizeReferencesAsync</td>
                    <td>0.005</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>optimizeReferences</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.compiler.beautifier.JsBeautifierTest (4 tests, 0.003s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>simpleBeautifyCall</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>largeFile</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>simpleBeautifyCallWithOptions</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.compiler.jsb.statements.ParserTest (17 tests, 0.047s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testParseStatementProperties</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testParseStatement</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIsStatement</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testScript7WithComments</td>
                    <td>0.005</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testClassBody</td>
                    <td>0.010</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testScript2WithComments</td>
                    <td>0.003</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testEvaluate</td>
                    <td>0.008</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIsCloseOf</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testScript1</td>
                    <td>0.003</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testScript2</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testScript3</td>
                    <td>0.003</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testScript4</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testScript5</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testScript6</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testScript7</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDeprecatedProduct</td>
                    <td>0.004</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.compiler.meta.NoteTest (4 tests, 0.002s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>docComment1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>docComment2</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>docComment3</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.compiler.sources.ClassDeclarationVisitorTest (3 tests, 0.011s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>checkVisitation</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>runDoxiVisitor</td>
                    <td>0.008</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.compiler.sources.ES6Test (8 tests, 0.529s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testEs6</td>
                    <td>0.014</td>
                    <td class="status-FAILED">FAILED</td>
                    <td>expected:<...                   [[pre,cur] = [
                                        cur,
                                        pre + cur
                                    ];
                                    return {
                                        done: false,
                                        value: cur
                                    };
                                }
                            };
                        }
                    };
                for (let n of fibonacci) {
                    if (n > 1000)  {
                        break;
                    }

                    console.log(n);
                }
            },
            generators: function() {
                let fibonacci = {
                        * [Symbol.iterator]() {
                            let pre = 0,
                                cur = 1;
                            for (; ; ) {
                                [pre,cur] = [
                                    cur,
                                    pre + cur
                                ];
                                yield cur;
                            }
                        }
                    };
                for (let n of fibonacci) {
                    if (n > 1000)  {
                        break;
                    }

                    console.log(n);
                }
                function * range(start, end, step) {
                    while (start < end) {
                        yield start;
                        start += step;
                    }
                }
                for (let i of range(0, 10, 2)) {
                    console.log(i);
                }
                let fibonacci2 = function *(numbers) {
                        let pre = 0,
                            cur = 1;
                        while (numbers-- > 0) {
                            [pre,cur] = [
                                cur,
                                pre + cur
                            ];
                            yield cur;
                        }
                    };
                for (let n of fibonacci(1000)) console.log(n);
                let numbers = [
                        ...fibonacci(1000)
                    ];
                let [n1,n2,n3,...others] = fibonacci(1000);
                class Clz {
                    * bar() {}

                }

                let Obj = {
                        * foo() {}
                    };
            },
            symbols: function() {
                Symbol.for("app.foo") === Symbol.for("app.foo");
                const foo = Symbol.for("app.foo");
                const bar = Symbol.for("app.bar");
                Symbol.keyFor(foo) === "app.foo";
                Symbol.keyFor(bar) === "app.bar";
                typeof foo === "symbol";
                typeof bar === "symbol";
                let obj = {};
                obj[foo] = "foo";
                obj[bar] = "bar";
                JSON.stringify(obj);
                Object.keys(obj);
                Object.getOwnPropertyNames(obj);
                Object.getOwnPropertySymbols(obj);
            },
            destructuringAssignment: function() {
                var list = [
                        1,
                        2,
                        3
                    ];
                var [a,,b] = list;
                [b,a] = [
                    a,
                    b
                ];
                var {
                        op,
                        lhs,
                        rhs
                    } = getASTNode();
                var {
                        op: a,
                        lhs: {
                            op: b
                        },
                        rhs: c
                    } = getASTNode();
                function f([name,val]) {
                    console.log(name, val);
                }
                function g({
                    name: n,
                    val: v
                }) {
                    console.log(n, v);
                }
                function h({
                    name,
                    val
                }) {
                    console.log(name, val);
                }
                f([
                    "bar",
                    42
                ]);
                g({
                    name: "foo",
                    val: 7
                });
                h({
                    name: "bar",
                    val: 42
                });
                var list = [
                        7,
                        42
                    ];
                var [a = 1,b = 2,c = 3,d]] = list;
          ...> but was:<...                   [[ pre, cur ] = [
                                        cur,
                                        pre + cur
                                    ];
                                    return {
                                        done: false,
                                        value: cur
                                    };
                                }
                            };
                        }
                    };
                for (let n of fibonacci) {
                    if (n > 1000)  {
                        break;
                    }

                    console.log(n);
                }
            },
            generators: function() {
                let fibonacci = {
                        * [Symbol.iterator]() {
                            let pre = 0,
                                cur = 1;
                            for (; ; ) {
                                [ pre, cur ] = [
                                    cur,
                                    pre + cur
                                ];
                                yield cur;
                            }
                        }
                    };
                for (let n of fibonacci) {
                    if (n > 1000)  {
                        break;
                    }

                    console.log(n);
                }
                function * range(start, end, step) {
                    while (start < end) {
                        yield start;
                        start += step;
                    }
                }
                for (let i of range(0, 10, 2)) {
                    console.log(i);
                }
                let fibonacci2 = function *(numbers) {
                        let pre = 0,
                            cur = 1;
                        while (numbers-- > 0) {
                            [ pre, cur ] = [
                                cur,
                                pre + cur
                            ];
                            yield cur;
                        }
                    };
                for (let n of fibonacci(1000)) console.log(n);
                let numbers = [
                        ...fibonacci(1000)
                    ];
                let [ n1, n2, n3, ...others ] = fibonacci(1000);
                class Clz {
                    * bar() {}

                }

                let Obj = {
                        * foo() {}
                    };
            },
            symbols: function() {
                Symbol.for("app.foo") === Symbol.for("app.foo");
                const foo = Symbol.for("app.foo");
                const bar = Symbol.for("app.bar");
                Symbol.keyFor(foo) === "app.foo";
                Symbol.keyFor(bar) === "app.bar";
                typeof foo === "symbol";
                typeof bar === "symbol";
                let obj = {};
                obj[foo] = "foo";
                obj[bar] = "bar";
                JSON.stringify(obj);
                Object.keys(obj);
                Object.getOwnPropertyNames(obj);
                Object.getOwnPropertySymbols(obj);
            },
            destructuringAssignment: function() {
                var list = [
                        1,
                        2,
                        3
                    ];
                var [ a, , b ] = list;
                [ b, a ] = [
                    a,
                    b
                ];
                var { op, lhs, rhs } = getASTNode();
                var { op: a, lhs: { op: b }, rhs: c } = getASTNode();
                function f([ name, val ]) {
                    console.log(name, val);
                }
                function g({ name: n, val: v }) {
                    console.log(n, v);
                }
                function h({ name, val }) {
                    console.log(name, val);
                }
                f([
                    "bar",
                    42
                ]);
                g({
                    name: "foo",
                    val: 7
                });
                h({
                    name: "bar",
                    val: 42
                });
                var list = [
                        7,
                        42
                    ];
                var [ a = 1, b = 2, c = 3, d ]] = list;
          ...></td>
                </tr>

                <tr>
                    <td>testEs6_2</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testEs6TranspileNoPolyfill</td>
                    <td>0.005</td>
                    <td class="status-FAILED">FAILED</td>
                    <td>expected:<...                   [[pre,cur] = [
                                cur,
                                pre + cur
                            ];
                            yield cur;
                        }
                    };
                for (let n of fibonacci(1000)) console.log(n);
                let numbers = [
                        ...fibonacci(1000)
                    ];
                let [n1,n2,n3,...others] = fibonacci(1000);
                class Clz {
                    * bar() {}

                }

                let Obj = {
                        * foo() {}
                    };
            },
            destructuringAssignment: function() {
                var list = [
                        1,
                        2,
                        3
                    ];
                var [a,,b] = list;
                [b,a] = [
                    a,
                    b
                ];
                var {
                        op,
                        lhs,
                        rhs
                    } = getASTNode();
                var {
                        op: a,
                        lhs: {
                            op: b
                        },
                        rhs: c
                    } = getASTNode();
                function f([name,val]) {
                    console.log(name, val);
                }
                function g({
                    name: n,
                    val: v
                }) {
                    console.log(n, v);
                }
                function h({
                    name,
                    val
                }) {
                    console.log(name, val);
                }
                f([
                    "bar",
                    42
                ]);
                g({
                    name: "foo",
                    val: 7
                });
                h({
                    name: "bar",
                    val: 42
                });
                var list = [
                        7,
                        42
                    ];
                var [a = 1,b = 2,c = 3,d]] = list;
          ...> but was:<...                   [[ pre, cur ] = [
                                cur,
                                pre + cur
                            ];
                            yield cur;
                        }
                    };
                for (let n of fibonacci(1000)) console.log(n);
                let numbers = [
                        ...fibonacci(1000)
                    ];
                let [ n1, n2, n3, ...others ] = fibonacci(1000);
                class Clz {
                    * bar() {}

                }

                let Obj = {
                        * foo() {}
                    };
            },
            destructuringAssignment: function() {
                var list = [
                        1,
                        2,
                        3
                    ];
                var [ a, , b ] = list;
                [ b, a ] = [
                    a,
                    b
                ];
                var { op, lhs, rhs } = getASTNode();
                var { op: a, lhs: { op: b }, rhs: c } = getASTNode();
                function f([ name, val ]) {
                    console.log(name, val);
                }
                function g({ name: n, val: v }) {
                    console.log(n, v);
                }
                function h({ name, val }) {
                    console.log(name, val);
                }
                f([
                    "bar",
                    42
                ]);
                g({
                    name: "foo",
                    val: 7
                });
                h({
                    name: "bar",
                    val: 42
                });
                var list = [
                        7,
                        42
                    ];
                var [ a = 1, b = 2, c = 3, d ]] = list;
          ...></td>
                </tr>

                <tr>
                    <td>testEs6_8to5</td>
                    <td>0.281</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testEs6_8to6</td>
                    <td>0.211</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testExtEs6</td>
                    <td>0.006</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testEs6Transpile</td>
                    <td>0.008</td>
                    <td class="status-FAILED">FAILED</td>
                    <td>expected:<...                   [[pre,cur] = [
                                        cur,
                                        pre + cur
                                    ];
                                    return {
                                        done: false,
                                        value: cur
                                    };
                                }
                            };
                        }
                    };
                for (let n of fibonacci) {
                    if (n > 1000)  {
                        break;
                    }

                    console.log(n);
                }
            },
            generators: function() {
                let fibonacci = {
                        * [Symbol.iterator]() {
                            let pre = 0,
                                cur = 1;
                            for (; ; ) {
                                [pre,cur] = [
                                    cur,
                                    pre + cur
                                ];
                                yield cur;
                            }
                        }
                    };
                for (let n of fibonacci) {
                    if (n > 1000)  {
                        break;
                    }

                    console.log(n);
                }
                function * range(start, end, step) {
                    while (start < end) {
                        yield start;
                        start += step;
                    }
                }
                for (let i of range(0, 10, 2)) {
                    console.log(i);
                }
                let fibonacci2 = function *(numbers) {
                        let pre = 0,
                            cur = 1;
                        while (numbers-- > 0) {
                            [pre,cur] = [
                                cur,
                                pre + cur
                            ];
                            yield cur;
                        }
                    };
                for (let n of fibonacci(1000)) console.log(n);
                let numbers = [
                        ...fibonacci(1000)
                    ];
                let [n1,n2,n3,...others] = fibonacci(1000);
                class Clz {
                    * bar() {}

                }

                let Obj = {
                        * foo() {}
                    };
            },
            symbols: function() {
                Symbol.for("app.foo") === Symbol.for("app.foo");
                const foo = Symbol.for("app.foo");
                const bar = Symbol.for("app.bar");
                Symbol.keyFor(foo) === "app.foo";
                Symbol.keyFor(bar) === "app.bar";
                typeof foo === "symbol";
                typeof bar === "symbol";
                let obj = {};
                obj[foo] = "foo";
                obj[bar] = "bar";
                JSON.stringify(obj);
                Object.keys(obj);
                Object.getOwnPropertyNames(obj);
                Object.getOwnPropertySymbols(obj);
            },
            destructuringAssignment: function() {
                var list = [
                        1,
                        2,
                        3
                    ];
                var [a,,b] = list;
                [b,a] = [
                    a,
                    b
                ];
                var {
                        op,
                        lhs,
                        rhs
                    } = getASTNode();
                var {
                        op: a,
                        lhs: {
                            op: b
                        },
                        rhs: c
                    } = getASTNode();
                function f([name,val]) {
                    console.log(name, val);
                }
                function g({
                    name: n,
                    val: v
                }) {
                    console.log(n, v);
                }
                function h({
                    name,
                    val
                }) {
                    console.log(name, val);
                }
                f([
                    "bar",
                    42
                ]);
                g({
                    name: "foo",
                    val: 7
                });
                h({
                    name: "bar",
                    val: 42
                });
                var list = [
                        7,
                        42
                    ];
                var [a = 1,b = 2,c = 3,d]] = list;
          ...> but was:<...                   [[ pre, cur ] = [
                                        cur,
                                        pre + cur
                                    ];
                                    return {
                                        done: false,
                                        value: cur
                                    };
                                }
                            };
                        }
                    };
                for (let n of fibonacci) {
                    if (n > 1000)  {
                        break;
                    }

                    console.log(n);
                }
            },
            generators: function() {
                let fibonacci = {
                        * [Symbol.iterator]() {
                            let pre = 0,
                                cur = 1;
                            for (; ; ) {
                                [ pre, cur ] = [
                                    cur,
                                    pre + cur
                                ];
                                yield cur;
                            }
                        }
                    };
                for (let n of fibonacci) {
                    if (n > 1000)  {
                        break;
                    }

                    console.log(n);
                }
                function * range(start, end, step) {
                    while (start < end) {
                        yield start;
                        start += step;
                    }
                }
                for (let i of range(0, 10, 2)) {
                    console.log(i);
                }
                let fibonacci2 = function *(numbers) {
                        let pre = 0,
                            cur = 1;
                        while (numbers-- > 0) {
                            [ pre, cur ] = [
                                cur,
                                pre + cur
                            ];
                            yield cur;
                        }
                    };
                for (let n of fibonacci(1000)) console.log(n);
                let numbers = [
                        ...fibonacci(1000)
                    ];
                let [ n1, n2, n3, ...others ] = fibonacci(1000);
                class Clz {
                    * bar() {}

                }

                let Obj = {
                        * foo() {}
                    };
            },
            symbols: function() {
                Symbol.for("app.foo") === Symbol.for("app.foo");
                const foo = Symbol.for("app.foo");
                const bar = Symbol.for("app.bar");
                Symbol.keyFor(foo) === "app.foo";
                Symbol.keyFor(bar) === "app.bar";
                typeof foo === "symbol";
                typeof bar === "symbol";
                let obj = {};
                obj[foo] = "foo";
                obj[bar] = "bar";
                JSON.stringify(obj);
                Object.keys(obj);
                Object.getOwnPropertyNames(obj);
                Object.getOwnPropertySymbols(obj);
            },
            destructuringAssignment: function() {
                var list = [
                        1,
                        2,
                        3
                    ];
                var [ a, , b ] = list;
                [ b, a ] = [
                    a,
                    b
                ];
                var { op, lhs, rhs } = getASTNode();
                var { op: a, lhs: { op: b }, rhs: c } = getASTNode();
                function f([ name, val ]) {
                    console.log(name, val);
                }
                function g({ name: n, val: v }) {
                    console.log(n, v);
                }
                function h({ name, val }) {
                    console.log(name, val);
                }
                f([
                    "bar",
                    42
                ]);
                g({
                    name: "foo",
                    val: 7
                });
                h({
                    name: "bar",
                    val: 42
                });
                var list = [
                        7,
                        42
                    ];
                var [ a = 1, b = 2, c = 3, d ]] = list;
          ...></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.compiler.sources.MarkupCompileDirectiveProcessorTest (3 tests, 0.003s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testMarkupJsGeneration</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMarkupParse</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.compiler.sources.MarkupDirectiveTest (9 tests, 0.001s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>debugDirectiveWithSpaces</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>debugEndDirective</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>debugDirective</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>debugWarnDirectiveWithSpaces</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>commentedScriptTag</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>debugEndDirectiveWithSpaces</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>complexIfDirective</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>debugWarnDirective</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>complexIfDirectiveWithSpaces</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.compiler.sources.ScriptDirectiveTest (8 tests, 0.001s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>debugDirectiveWithSpaces</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>debugEndDirective</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>debugDirective</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>debugWarnDirectiveWithSpaces</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>debugEndDirectiveWithSpaces</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>complexIfDirective</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>debugWarnDirective</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>complexIfDirectiveWithSpaces</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.compiler.sources.VariableRenamerTest (6 tests, 0.185s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testVariableRenamer2</td>
                    <td>0.003</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>tesetCKEditor</td>
                    <td>0.146</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testVariableRenamerMinifiedInput</td>
                    <td>0.019</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testUnderscore</td>
                    <td>0.009</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testVariableRenamer</td>
                    <td>0.006</td>
                    <td class="status-FAILED">FAILED</td>
                    <td>expected:<... {
            var {[
                    baz: bip,
                    foo: {
                        baz: bar
                    }
                } = this.foo(b, a),
                [first,second,,third] = [
                    1,
                    2,
                    3,
                    4,
                    5
                ];
            console.log(bip, bar, first, second, third);
        },
        func() {
            var a = this;
            var b = a.performance && a.performance.now ? function() {} : Ext.now;
        },
        func2() {
            var b = Array.prototype,
                a = b.slice,
                f = (function() {
                    var a = [],
                        b,
                        c = 20;
                    if (!a.splice) {
                        return false;
                    }
                    // This detects a bug in IE8 splice method:
                    // see http://social.msdn.microsoft.com/Forums/en-US/iewebdevelopment/thread/6e946d03-e09f-4b22-a4dd-cd5e276bf05a/
                    while (c--) {
                        a.push("A");
                    }
                    a.splice(15, 0, "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F");
                    b = a.length;
                    //41
                    a.splice(13, 0, "XXX");
                    // add one element
                    if (b + 1 !== a.length) {
                        return false;
                    }
                    // end IE8 bug
                    return true;
                }()),
                e = 'indexOf' in b,
                d = true;
            var g = {
                    slice: ([
                        1,
                        2
                    ].slice(1, undefined).length ? function(b, c, d) {
                        return a.call(b, c, d);
                    } : function(b, c, d) {
                        // see http://jsperf.com/slice-fix
                        if (typeof c === 'undefined') {
                            return a.call(b);
                        }
                        if (typeof d === 'undefined') {
                            return a.call(b, c);
                        }
                        return a.call(b, c, d);
                    })
                };
            var c;
            Ext.Version = c = function(a, g) {
                var f = this,
                    c = f.padModes,
                    e, k, d, j, i, h, b;
                if (a.isVersion) {
                    a = a.version;
                }
                f.version = b = String(a).toLowerCase().replace(underscoreRe, '.').replace(plusMinusRe, '');
                e = b.charAt(0);
                if (e in c) {
                    b = b.substring(1);
                    d = c[e];
                } else {
                    d = g ? c[g] : 0;
                }
                // careful - NaN is falsey!
                f.pad = d;
            };
        },
        asdfasdf: function(a) {
            var b = 1,
                a;
            console.log(a);
        },
        someFunction() {
            var a = "world",
                b = function() {
                    var b = (a || (a = "hello")).length,
                        c;
                    console.log(b);
                };
        },
        testFnName() {
            var a = function() {
                    function a() {
                        console.log("the funcName fn");
                    }
                    return a();
                };
        },
        testCatchBlock() {
            function a(h, i, a, j) {
                var k = this,
                    n = xds.component.Definition.aliasNamepool,
                    e = i.cn,
                    d = k.getModelFromSnapshot(i),
                    b = d.getInstance(),
                    l = b.getUserAlias(),
                    c, g;
                if (a) {
                    a = a.getInstance();
                    // We cannot accept the root instance as a parent argument, because
                    // later in this function we determine validity checks based on the
                    // presence of the parentInstance argument
                    if (a === h.getRootNode().getInstance()) {
                        a = null;
                    }
                } else {
                    b.validAsTopInstance = true;
                }
                // Validate the instance can go under parent
                if (!b.$isValidParent(a || null)) {
                    return null;
                }
                if (a) {
                    if (!a.$isValidChild(b)) {
                        return null;
                    }
                }
                if (a) {
                    if (a.$onBeforeRestoreChild(b) === false) {
                        return null;
                    }
                    c = a.getModel();
                } else {
                    c = h.getRootNode();
                }
                if (b.$onBeforeRestore(a) === false) {
                    return null;
                }
                if (j) {
                    g = j.getModel();
                }
                if (b.name) {
                    xds.component.Definition.addToNamePool(b.name);
                }
                xds.component.Definition.addToAliasNamePool(l, i.type);
                // we don't want add/addchild/etc. events firing as a result of model insertion
                if (a) {
                    a.suspendEvents();
                }
                b.suspendEvents();
                if (g) {
                    c.insertBefore(d, g);
                } else {
                    c.appendChild(d);
                }
                if (a) {
                    a.resumeEvents();
                    a.$onRestoreChild(b);
                }
                b.resumeEvents();
                if (e) {
                    for (var f = 0,
                        m = e.length; f < m; f++) {
                        try {
                            k.restoreTo(h, e[f], d);
                        } catch (o) {
                            xds.ui.Ack.display('Unsupported sub component type: ' + e[f].type);
                        }
                    }
                }
                b.$onRestore(a);
                return b;
            }
        },
        methodWithDefaults: function(a = "bar") {
            console.log(a);
        },
        methodWithDefaultsNew: function(a = new b(24)) {
            console.log(a]);
        },
      ...> but was:<... {
            var {[baz: bip, foo: { baz: bar} } = this.foo(b, a),
                [first, second, , third] = [
                    1,
                    2,
                    3,
                    4,
                    5
                ];
            console.log(bip, bar, first, second, third);
        },
        func() {
            var a = this;
            var b = a.performance && a.performance.now ? function() {} : Ext.now;
        },
        func2() {
            var b = Array.prototype,
                a = b.slice,
                f = (function() {
                    var a = [],
                        b,
                        c = 20;
                    if (!a.splice) {
                        return false;
                    }
                    // This detects a bug in IE8 splice method:
                    // see http://social.msdn.microsoft.com/Forums/en-US/iewebdevelopment/thread/6e946d03-e09f-4b22-a4dd-cd5e276bf05a/
                    while (c--) {
                        a.push("A");
                    }
                    a.splice(15, 0, "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F", "F");
                    b = a.length;
                    //41
                    a.splice(13, 0, "XXX");
                    // add one element
                    if (b + 1 !== a.length) {
                        return false;
                    }
                    // end IE8 bug
                    return true;
                }()),
                e = 'indexOf' in b,
                d = true;
            var g = {
                    slice: ([
                        1,
                        2
                    ].slice(1, undefined).length ? function(b, c, d) {
                        return a.call(b, c, d);
                    } : function(b, c, d) {
                        // see http://jsperf.com/slice-fix
                        if (typeof c === 'undefined') {
                            return a.call(b);
                        }
                        if (typeof d === 'undefined') {
                            return a.call(b, c);
                        }
                        return a.call(b, c, d);
                    })
                };
            var c;
            Ext.Version = c = function(a, g) {
                var f = this,
                    c = f.padModes,
                    e, k, d, j, i, h, b;
                if (a.isVersion) {
                    a = a.version;
                }
                f.version = b = String(a).toLowerCase().replace(underscoreRe, '.').replace(plusMinusRe, '');
                e = b.charAt(0);
                if (e in c) {
                    b = b.substring(1);
                    d = c[e];
                } else {
                    d = g ? c[g] : 0;
                }
                // careful - NaN is falsey!
                f.pad = d;
            };
        },
        asdfasdf: function(a) {
            var b = 1,
                a;
            console.log(a);
        },
        someFunction() {
            var a = "world",
                b = function() {
                    var b = (a || (a = "hello")).length,
                        c;
                    console.log(b);
                };
        },
        testFnName() {
            var a = function() {
                    function a() {
                        console.log("the funcName fn");
                    }
                    return a();
                };
        },
        testCatchBlock() {
            function a(h, i, a, j) {
                var k = this,
                    n = xds.component.Definition.aliasNamepool,
                    e = i.cn,
                    d = k.getModelFromSnapshot(i),
                    b = d.getInstance(),
                    l = b.getUserAlias(),
                    c, g;
                if (a) {
                    a = a.getInstance();
                    // We cannot accept the root instance as a parent argument, because
                    // later in this function we determine validity checks based on the
                    // presence of the parentInstance argument
                    if (a === h.getRootNode().getInstance()) {
                        a = null;
                    }
                } else {
                    b.validAsTopInstance = true;
                }
                // Validate the instance can go under parent
                if (!b.$isValidParent(a || null)) {
                    return null;
                }
                if (a) {
                    if (!a.$isValidChild(b)) {
                        return null;
                    }
                }
                if (a) {
                    if (a.$onBeforeRestoreChild(b) === false) {
                        return null;
                    }
                    c = a.getModel();
                } else {
                    c = h.getRootNode();
                }
                if (b.$onBeforeRestore(a) === false) {
                    return null;
                }
                if (j) {
                    g = j.getModel();
                }
                if (b.name) {
                    xds.component.Definition.addToNamePool(b.name);
                }
                xds.component.Definition.addToAliasNamePool(l, i.type);
                // we don't want add/addchild/etc. events firing as a result of model insertion
                if (a) {
                    a.suspendEvents();
                }
                b.suspendEvents();
                if (g) {
                    c.insertBefore(d, g);
                } else {
                    c.appendChild(d);
                }
                if (a) {
                    a.resumeEvents();
                    a.$onRestoreChild(b);
                }
                b.resumeEvents();
                if (e) {
                    for (var f = 0,
                        m = e.length; f < m; f++) {
                        try {
                            k.restoreTo(h, e[f], d);
                        } catch (o) {
                            xds.ui.Ack.display('Unsupported sub component type: ' + e[f].type);
                        }
                    }
                }
                b.$onRestore(a);
                return b;
            }
        },
        methodWithDefaults: function(foo = "bar") {
            console.log(foo);
        },
        methodWithDefaultsNew: function(foo = new ArrayBuffer(24.0)) {
            console.log(foo]);
        },
      ...></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.generator.DescriptorTest (13 tests, 0.010s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>validateMax</td>
                    <td>0.003</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>validateMin</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>basicParam</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>validateLength</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>validateMinMax</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>validateExclusion</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>conditionalIncludeDeep</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>basicDescriptor</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>validateInclusion</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>conditionalInclude</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>conditionalExcludeDeep</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>validatePresence</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>conditionalExclude</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.generator.GeneratorCommandsTest (3 tests, 11.757s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testGenerateMultiAppWorkspace</td>
                    <td>7.536</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testGenerateSingleApp</td>
                    <td>4.197</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.generator.GeneratorTest (10 tests, 0.488s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testSenchaTemplateDefault</td>
                    <td>0.057</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>templateWithDescriptor</td>
                    <td>0.051</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testPlainFile</td>
                    <td>0.049</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testApacheVelocitySeriallyReusable</td>
                    <td>0.092</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testArbitraryTree</td>
                    <td>0.068</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testApacheVelocityDefault</td>
                    <td>0.041</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testSenchaTemplateSeriallyReusable</td>
                    <td>0.041</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testSenchaTemplate</td>
                    <td>0.042</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testApacheVelocity</td>
                    <td>0.043</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.inspector.InspectorConnectorTest (3 tests, 0.326s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testManifestAdditions</td>
                    <td>0.227</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testConnectorSetup</td>
                    <td>0.097</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.page.AppJsonBuilderTest (24 tests, 0.785s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testMicroloadModeHtmlOutput</td>
                    <td>0.014</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMicroloadModeRemoteFullOutput</td>
                    <td>0.014</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMicroloadModeBootstrap</td>
                    <td>0.041</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMicroloadModeNoSdkSlicerPage</td>
                    <td>0.022</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testAbsPathLoad</td>
                    <td>0.029</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMicroloadModeFullOutput</td>
                    <td>0.038</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testSplitMicroloadModeFullOutput</td>
                    <td>0.072</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testForceSplitMarkupModeHtmlOutput</td>
                    <td>0.031</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMicroloadModeRemoteBootstrap</td>
                    <td>0.036</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMicroloadModeNoSdkBootstrap</td>
                    <td>0.028</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMarkupModeHtmlOutput</td>
                    <td>0.034</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMicroloadNoSdkAppLoad</td>
                    <td>0.068</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMicroloadModeSlicerPage</td>
                    <td>0.027</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testForceSplitMarkupModeFullOutput</td>
                    <td>0.017</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMarkupModeFullOutput</td>
                    <td>0.013</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMicroloadAppLoad</td>
                    <td>0.053</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testAbsPathBootstrap</td>
                    <td>0.012</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testSplitMicroloadAppLoad</td>
                    <td>0.016</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testAbsPathsFullOutput</td>
                    <td>0.037</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMarkupModeLoad</td>
                    <td>0.067</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMarkupModeBootstrap</td>
                    <td>0.051</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testMicroloadModeNoSdkFullOutput</td>
                    <td>0.023</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFullAppManifest</td>
                    <td>0.033</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.pkg.BaseRepositoryTest (1 tests, 0.000s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.pkg.EntitlementTest (2 tests, 3.159s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testEntitlements</td>
                    <td>3.069</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.pkg.LocalRepositoryTest (7 tests, 2.758s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>transitiveTest</td>
                    <td>0.135</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>addSignPackage</td>
                    <td>2.036</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>addFile</td>
                    <td>0.121</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>deletePackage</td>
                    <td>0.100</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>initRepo</td>
                    <td>0.058</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>syncRemote</td>
                    <td>0.218</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.pkg.LockerTest (8 tests, 0.219s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>sealHasOwnerKey</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>sealHasTrusteeeKey</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>sealCanGenerateUsableActivationKey</td>
                    <td>0.004</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>sealCanBeOpenedByTrustee</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>sealReplacesLocker</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>sealCanBeOpenedByOwner</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>sealHasProperContent</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.pkg.ModelTest (13 tests, 0.006s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testPackageAlternateName</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testPackageSavesExtraContent</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>parsePackageWithAlternateName</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>loadPackage</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>savePackageWithArchitectProp</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>parsePackageNoAlternateName</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>parsePackage2</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testPackageConflict1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testPackageSignsExtraContent</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>parsePackage</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testPackageResolve1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>templatePackage</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.slicer.CssDirectiveProcessorTest (7 tests, 0.002s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testSimpleInput</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>ifSlicerDirectiveTest</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>ifSlicerEndDirectiveTest</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>xSliceEndDirectiveWithSpaces</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>xSliceDirectiveWithSpaces</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>xSliceDirective</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>xSliceEndDirective</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.slicer.SlicerTest (12 tests, 0.389s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testEasyButtonPngDefaultSmallCorners</td>
                    <td>0.030</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testAudit</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testPanelHeaderDefaultFramedTopNeptune</td>
                    <td>0.013</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFrameWidget</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testGridHeader</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testEasyButtonDefaultSmallCorners</td>
                    <td>0.044</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testButtonDefaultSmall</td>
                    <td>0.006</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testPanelHeaderDefaultFramedLeft</td>
                    <td>0.012</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testButtonDefaultSmallOver</td>
                    <td>0.005</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStretchWidget</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testPanelHeaderDefaultFramedTop</td>
                    <td>0.004</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.splitters.CssFileSplitterTest (6 tests, 0.745s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testSplitSmallFile</td>
                    <td>0.144</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testSplitLargeContent</td>
                    <td>0.212</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testSplitLargeFile</td>
                    <td>0.165</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testSplitLargeContent2</td>
                    <td>0.179</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testSplitSmallContent</td>
                    <td>0.044</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.upgrade.BuildXmlUpdateTest (2 tests, 0.003s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testNpmCompatTransform</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.tools.upgrade.NpmCompatTransformTest (2 tests, 0.002s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testNpmCompatTransform</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.CharsetDetectorTest (8 tests, 0.002s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>matchMarkupComment</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>matchMarkupCommentUnMatched</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>matchMarkupCommenInvalidName</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>matchJavascriptComment</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>matchJavascriptCommentUnMatched</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>matchJavascriptCommentInvalidName</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>matchMarkupCommentWithSpaces</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.ConfigurationTest (11 tests, 0.003s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>convertsToFile</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>loadJsonPropertiesWithPrefixTest</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>loadJsonPropertiesTest</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>expansion</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>propertyExpansionTest</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>convertsToNullFile</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>loadJsonPropertiesWithoutRequiresTest</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>lazyEvaluationWithEach</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>lazyEvaluationWithSet</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>lazyEvaluation</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.ConverterTest (172 tests, 0.016s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testBooleanFalseToFloat</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongToDoublePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testBooleanFalseToShort</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testBooleanTrueToByte</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testBooleanTrueToLong</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToFloatPrimitive1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToFloatPrimitive2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToLongPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToDouble</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortToShortPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testBooleanFalseToDouble</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerToIntegerPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringNoToBooleanFalse</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToLongPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerArrayPrimitiveToWrapper</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteToFloat</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteToShort</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToDoublePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToBooleanTruePrimitive1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testBooleanFalseToString</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteToLongPrimitive</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToInteger</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongToByte</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongToLong</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testBooleanTrueToDouble</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToBooleanTrue</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleArrayPrimitiveToWrapper</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortToDouble</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongToBooleanTruePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteToInteger</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToString1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToString2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToString3</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerArrayWrapperToPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerToDouble</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testBooleanTrueToString</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringZeroToBooleanFalse</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToDouble1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToDouble2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>supportsUpconversion</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToFloatPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testBooleanTrueToFloat</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToShortPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testBooleanTrueToShort</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerToBooleanFalsePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortToString</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToBooleanTruePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToFloat1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToFloat2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatArrayPrimitiveToWrapper</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongToLongPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testEnumIgnoreCase</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerToString</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteToShortPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortToFloatPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortToDoublePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongToBooleanFalse</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortToBooleanTruePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerToLongPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToDouble</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleArrayWrapperToPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongToShortPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortToInteger</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToString1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToString2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToBytePrimitive1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToBytePrimitive2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteToByte</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteToLong</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToBooleanFalse</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToBooleanFalse</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteToBooleanFalse</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToShort1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToBooleanTrue</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToBooleanFalsePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortArrayPrimitiveToWrapper</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongToIntegerPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringTrueToBooleanTrue</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToInteger1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToInteger2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerToShortPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToShortPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringFalseToBooleanFalse</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortToBytePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToShortPrimitive1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerToBooleanTruePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteToIntegerPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortToByte</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortToLong</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatArrayWrapperToPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToBytePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToBooleanFalsePrimitive1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToBooleanFalsePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToBytePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToFloat</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToShort</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongToDouble</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteToBooleanTruePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteArrayPrimitiveToWrapper</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToIntegerPrimitive1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToIntegerPrimitive2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToByte1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToByte2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToFile1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToLong1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToLong2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToShort</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortArrayWrapperToPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongToFloat</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToFloatPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongToInteger</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerToByte</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerToLong</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongToShort</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testEnumExact</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongArrayPrimitiveToWrapper</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerToFloat</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteToBytePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerToShort</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToDoublePrimitive1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToDoublePrimitive2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortToBooleanFalsePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToIntegerPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteToDouble</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToIntegerPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerToBooleanFalse</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerToDoublePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToDate</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteToFloatPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerToBooleanTrue</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongToString</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testBooleanFalseToByte</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testBooleanFalseToLong</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortToIntegerPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToBooleanTruePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongToFloatPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToInteger</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortToBooleanFalse</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortToFloat</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToDoublePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortToShort</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteToDoublePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteToString</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongToBooleanTrue</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteToBooleanTrue</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteArrayWrapperToPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerToFloatPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortToBooleanTrue</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToFloat</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToShort</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToString1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToString2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToString3</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongArrayWrapperToPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToByte</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToLong</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testByteToBooleanFalsePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToLongPrimitive1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringToLongPrimitive2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongToBytePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringOneToBooleanTrue</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDateToString</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFloatToShortPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testStringYesToBooleanTrue</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testLongToBooleanFalsePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testBooleanFalseToInteger</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortToLongPrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerToBytePrimitive</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToByte</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDoubleToLong</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testIntegerToInteger</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testBooleanTrueToInteger</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.DesktopUtilTest (2 tests, 0.001s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testFileDialog</td>
                    <td>0.000</td>
                    <td class="status-SKIPPED">SKIPPED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.Diff3Test (9 tests, 0.003s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>mergeTest1</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>mergeTest2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>mergeTest3</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>noChanges30Lines</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>conflictTest1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>conflictTest2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>conflictTest3</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>noChangesOneLine</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.FileUtilTest (12 tests, 0.016s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>updateZip</td>
                    <td>0.006</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testWriteFileDataNoCreateDir</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>detectMarkupCharsetTag</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testEnsurePathExistsCreateFile</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>readZipFileEntry1</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>readZipFileEntry2</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testWriteFileDataCreateFile</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testEnsurePathExistsCreateDir</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testEnsurePathExistsMustExist</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testGetRelativePath</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testWriteFileDataCreateDir</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.LocatorTest (3 tests, 0.000s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>basicPath</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>baseDir</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.NameUtilTest (2 tests, 0.034s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testAppNameToNamespace</td>
                    <td>0.033</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.NetworkUtilTest (3 tests, 0.001s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testDetectAvailablePort</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>getLocalAddress</td>
                    <td>0.000</td>
                    <td class="status-SKIPPED">SKIPPED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.ObjectUtilTest (1 tests, 0.000s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>comparesObjectsForEquivalency</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.PlatformUtilTest (3 tests, 0.000s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testComplexJavaVersion</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testSimpleJavaVersion</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testShortJavaVersion</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.ReflectionUtilTest (2 tests, 0.097s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testNewInstanceWithArgs</td>
                    <td>0.097</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.RegexUtilTest (7 tests, 0.001s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>wildcard1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>wildcard2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>basicPath</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>globPath</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>globPath2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>alphaNumeric</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>globFileName</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.StringUtilTest (27 tests, 0.005s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testRemoveDuplicateLineEndings</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDashifyFooBarHTML5</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDashifyFooBarHTMLStuff</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testSplit</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testJoin</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>shortestPrefix0</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>shortestPrefix1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>shortestPrefix2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>shortestPrefix3</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>shortestPrefix4</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>commonPrefixLengthLongerShorter</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>commonPrefixLengthShorterLonger</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>commonPrefixLengthNone</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>jsonCommentStripCombination</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>commonPrefixLengthEqual</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>commonPrefixLengthDivergent</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>commonPrefixLength1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>commonPrefixLength2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>commonPrefixLength3</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>jsonCommentStripMultiLine</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>jsonCommentStripSingleLine</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDashifyFooBarHTML</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDashifyFoo</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDashifyFooBar</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testDashifyFooBarHTML5Stuff</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testReplaceAllRegex</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.VersionNameTest (17 tests, 0.002s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>nameSlashVersionDashVersion</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>nameAtStar</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>nameSlashStar</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>nameAtDashVersion</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>nameAtVersionDashVersion</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>nameAtVersionDash</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>nameAtVersionPlus</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>nameAtDotDotDot</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>nameAtVersionDashVersionsQuestion</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>nameSlashDotDotDot</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>nameAtVersion</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>nameSlashVersion</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>nameAtVersionQuestion</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>nameSlashVersionDash</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>nameSlashVersionPlus</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testFrameworkSpecifiers</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.VersionRangeTest (21 tests, 0.002s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>intersectMinEqualsMax</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>intersectsMaxEqualsMin</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>intersectsOutside</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>intersectsMinEqualsMax</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>intersectsOverlap1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>intersectsOverlap2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>intersectsDisjointGreater</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>containsEqualToMax</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>containsEqualToMin</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>intersectOutside</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>containsGreaterThanMax</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>containsBetweenMinAndMax</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>intersectsInside</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>intersectOverlap1</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>intersectOverlap2</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>containsLessThanMin</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>intersectInside</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>intersectDisjointLess</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>intersectsDisjointLess</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>intersectMaxEqualsMin</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>intersectDisjointGreater</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.VersionTest (13 tests, 0.001s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>cleanVersionString</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>compareEquals</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>compareSameLengthLess</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testComparisons</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>parseNoDots</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>parseOneDot</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>compareLongerToShorterLess</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>compareLongerToShorterGreater</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testCleanVersionString</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>compareSameLengthGreater</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>compareShorterToLongerLess</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>compareShorterToLongerGreater</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>parseTwoDots</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.XmlBuilderTest (9 tests, 0.001s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>oneElementOneAttr</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>oneElementTwoChildren</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>nestedElementWithTextAndEscapes</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>nestedElementWithText</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>oneElementTwoAttrs</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>oneElementOneChild</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>oneElementTwoChildrenWithAttrs</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>oneElementNoAttrs</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.XmlParserTest (3 tests, 0.004s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>parseOneEntity</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>parseTwoEntitiesWithAttribs</td>
                    <td>0.002</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>parseOneEntityWithAttribs</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.ZipUtilTest (1 tests, 0.004s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>createsZipArchivesFromAListOfZippingDescriptors</td>
                    <td>0.004</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.filters.FilterChainTest (2 tests, 0.001s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testExclusiveFilterChain</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testInclusiveFilterChain</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.filters.RegexFilterTest (4 tests, 0.001s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testExclusiveSingleFilter</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testExclusiveMultiFilter</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testInclusiveSingleFilter</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testInclusiveMultiFilter</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.filters.SubstringFilterTest (4 tests, 0.001s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>testExclusiveSingleFilter</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testExclusiveMultiFilter</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testInclusiveSingleFilter</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testInclusiveMultiFilter</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.fs.FsMonitorTest (10 tests, 12.743s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>deleteGrandparentDirectory</td>
                    <td>0.790</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>dirCreate</td>
                    <td>0.625</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>deeplyNestedFileCreate</td>
                    <td>0.948</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>deeplyNestedFileModify</td>
                    <td>1.243</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>nestedFileCreate</td>
                    <td>5.704</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>nestedFileModify</td>
                    <td>1.252</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>deleteParentDirectory</td>
                    <td>0.617</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>fileCreate</td>
                    <td>0.619</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>fileModify</td>
                    <td>0.934</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.001</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.http.BasicResponderTest (4 tests, 0.309s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>helloWorld</td>
                    <td>0.102</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>helloWorld2</td>
                    <td>0.096</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>echoWorld</td>
                    <td>0.106</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>testNOP</td>
                    <td>0.000</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

    <div class="test-suite">
        <h3>com.sencha.util.http.ServerTest (3 tests, 0.285s)</h3>
        <table>
            <thead>
                <tr>
                    <th>Test Case</th>
                    <th>Time (s)</th>
                    <th>Status</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>

                <tr>
                    <td>respondersCanBeMountedIntoContexts</td>
                    <td>0.095</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>directoriesCanBeMountedIntoContexts</td>
                    <td>0.096</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

                <tr>
                    <td>directoriesAndResponderCanCoexistAsContexts</td>
                    <td>0.093</td>
                    <td class="status-PASSED">PASSED</td>
                    <td></td>
                </tr>

            </tbody>
        </table>
    </div>

</body>
</html>
