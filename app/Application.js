Ext.define('MyAppName.Application', {
    extend: 'Ext.app.Application',
    
    name: 'MyAppName',

    launch: function() {
        console.log('Application launched - Running ES6+ Feature Tests');
        this.runAllTests();
    },

    runAllTests: function() {
        // ES6/ES2015 Tests
        this.testVariableScoping();
        this.testArrowFunctions();
        this.testClasses();
        this.testEnhancedObjectLiterals();
        this.testTemplateLiterals();
        this.testDestructuring();
        this.testDefaultParameters();
        this.testRestParameter();
        this.testSpreadOperator();
        this.testIteratorsForOf();
        this.testGenerators();
        this.testModules();
        this.testSet();
        this.testWeakSet();
        this.testMap();
        this.testWeakMap();
        this.testUnicode();
        this.testProxies();
        this.testSymbols();
        this.testPromises();
        this.testReflect();
        this.testBinaryOctal();
        this.testProperTailCalls();
        this.testArrayFindMethods();
        
        // ES2016/ES7 Tests
        this.testES2016Features();
        
        // ES2017/ES8 Tests
        this.testES2017Features();
        
        // ES2018/ES9 Tests
        this.testES2018Features();
        
        // ES2019/ES10 Tests
        this.testES2019Features();
        
        // ES2020/ES11 Tests
        this.testES2020Features();
        
        // ES2021/ES12 Tests
        this.testES2021Features();
        
        // ES2022/ES13 Tests
        this.testES2022Features();
        
        // ES2023/ES14 Tests
        this.testES2023Features();
        
        // ES2024/ES15 Tests
        this.testES2024Features();
    },

    // 1. Variable Scoping (let, const)
    testVariableScoping: function() {
        console.log('\n=== 1. Variable Scoping Test ===');
        
        // Block scoping with let
        if (true) {
            let blockScoped = 'I am block scoped';
            const CONSTANT = 'I cannot be reassigned';
            console.log('Inside block:', blockScoped, CONSTANT);
        }
        
        console.log('Variable scoping test completed');
    },

    // 2. Arrow Functions
    testArrowFunctions: function() {
        console.log('\n=== 2. Arrow Functions Test ===');
        
        const numbers = [1, 2, 3, 4, 5];
        
        // Arrow function with implicit return
        const doubled = numbers.map(n => n * 2);
        console.log('Doubled:', doubled);
        
        // Arrow function with block body
        const filtered = numbers.filter(n => {
            return n % 2 === 0;
        });
        console.log('Even numbers:', filtered);
        
        // Arrow function preserving 'this' context
        const obj = {
            name: 'Test Object',
            regularFunction: function() {
                return function() {
                    return this.name; // 'this' is undefined or global
                };
            },
            arrowFunction: function() {
                return () => {
                    return this.name; // 'this' is preserved
                };
            }
        };
        
        console.log('Regular function this:', obj.regularFunction()());
        console.log('Arrow function this:', obj.arrowFunction()());
    },

    // 3. Classes
    testClasses: function() {
        console.log('\n=== 3. Classes Test ===');
        
        class Animal {
            constructor(name, species) {
                this.name = name;
                this.species = species;
            }
            
            speak() {
                return `${this.name} makes a sound`;
            }
            
            static getSpeciesCount() {
                return 'Many species exist';
            }
        }
        
        class Dog extends Animal {
            constructor(name, breed) {
                super(name, 'Canine');
                this.breed = breed;
            }
            
            speak() {
                return `${this.name} barks`;
            }
            
            get info() {
                return `${this.name} is a ${this.breed}`;
            }
        }
        
        const dog = new Dog('Buddy', 'Golden Retriever');
        console.log('Dog speaks:', dog.speak());
        console.log('Dog info:', dog.info);
        console.log('Static method:', Animal.getSpeciesCount());
    },

    // 4. Enhanced Object Literals
    testEnhancedObjectLiterals: function() {
        console.log('\n=== 4. Enhanced Object Literals Test ===');
        
        const name = 'JavaScript';
        const version = 'ES6';
        
        const obj = {
            // Property shorthand
            name,
            version,
            
            // Method shorthand
            greet() {
                return `Hello from ${this.name}`;
            },
            
            // Computed property names
            [`is${name}Awesome`]: true,
            [version + 'Features']: ['classes', 'arrows', 'destructuring']
        };
        
        console.log('Enhanced object:', obj);
        console.log('Greeting:', obj.greet());
        console.log('Computed property:', obj.isJavaScriptAwesome);
    },

    // 5. Template Literals 
    testTemplateLiterals: function() {
        console.log('\n=== 5. Template Literals Test ===');
        
        const user = 'Developer';
        const task = 'testing ES6 features';
        
        // Basic template literal
        const message = `Hello ${user}, you are ${task}!`;
        console.log('Template literal:', message);
        
        // Multi-line template literal
        const multiLine = `
            This is a multi-line
            template literal that
            preserves formatting
        `;
        console.log('Multi-line:', multiLine);
        
        // Tagged template literal
        function highlight(strings, ...values) {
            return strings.reduce((result, string, i) => {
                return result + string + (values[i] ? `**${values[i]}**` : '');
            }, '');
        }
        
        const tagged = highlight`User ${user} is ${task}`;
        console.log('Tagged template:', tagged);
    },

    // 6. Destructuring
    testDestructuring: function() {
        console.log('\n=== 6. Destructuring Test ===');
        
        // Array destructuring
        const colors = ['red', 'green', 'blue', 'yellow'];
        const [primary, secondary, ...rest] = colors;
        console.log('Array destructuring:', { primary, secondary, rest });
        
        // Object destructuring
        const person = {
            firstName: 'John',
            lastName: 'Doe',
            age: 30,
            address: {
                city: 'New York',
                country: 'USA'
            }
        };
        
        const { firstName, lastName, age, address: { city } } = person;
        console.log('Object destructuring:', { firstName, lastName, age, city });
        
        // Function parameter destructuring
        const processUser = ({ name, email = 'no-email' }) => {
            return `User: ${name}, Email: ${email}`;
        };
        
        console.log('Parameter destructuring:', processUser({ name: 'Jane' }));
    },

    // 7. Default Parameters
    testDefaultParameters: function() {
        console.log('\n=== 7. Default Parameters Test ===');
        
        function greet(name = 'World', greeting = 'Hello', punctuation = '!') {
            return `${greeting}, ${name}${punctuation}`;
        }
        
        console.log('No args:', greet());
        console.log('One arg:', greet('JavaScript'));
        console.log('All args:', greet('ES6', 'Welcome', '!!!'));
        
        // Default parameters with functions
        function createUser(name, role = getDefaultRole()) {
            return { name, role };
        }
        
        function getDefaultRole() {
            return 'user';
        }
        
        console.log('Function default:', createUser('Alice'));
    },

    // 8. Rest Parameter
    testRestParameter: function() {
        console.log('\n=== 8. Rest Parameter Test ===');
        
        function sum(first, ...numbers) {
            console.log('First:', first, 'Rest:', numbers);
            return first + numbers.reduce((a, b) => a + b, 0);
        }
        
        console.log('Sum result:', sum(1, 2, 3, 4, 5));
        
        function logArguments(...args) {
            args.forEach((arg, index) => {
                console.log(`Arg ${index}:`, arg);
            });
        }
        
        logArguments('a', 'b', 'c', 'd');
    },

    // 9. Spread Operator
    testSpreadOperator: function() {
        console.log('\n=== 9. Spread Operator Test ===');
        
        // Array spread
        const arr1 = [1, 2, 3];
        const arr2 = [4, 5, 6];
        const combined = [...arr1, ...arr2];
        console.log('Array spread:', combined);
        
        // Object spread
        const obj1 = { a: 1, b: 2 };
        const obj2 = { c: 3, d: 4 };
        const merged = { ...obj1, ...obj2, e: 5 };
        console.log('Object spread:', merged);
        
        // Function call spread
        function multiply(x, y, z) {
            return x * y * z;
        }
        
        const numbers = [2, 3, 4];
        console.log('Function spread:', multiply(...numbers));
    },

    // 10. Iterators & For..of
    testIteratorsForOf: function() {
        console.log('\n=== 10. Iterators & For..of Test ===');
        
        const iterable = ['a', 'b', 'c'];
        
        // For..of loop
        console.log('For..of loop:');
        for (const value of iterable) {
            console.log('Value:', value);
        }
        
        // Custom iterator
        const customIterable = {
            data: [1, 2, 3, 4, 5],
            [Symbol.iterator]() {
                let index = 0;
                return {
                    next: () => {
                        if (index < this.data.length) {
                            return { value: this.data[index++], done: false };
                        }
                        return { done: true };
                    }
                };
            }
        };
        
        console.log('Custom iterator:');
        for (const value of customIterable) {
            console.log('Custom value:', value);
        }
    },

    // 11. Generators
    testGenerators: function() {
        console.log('\n=== 11. Generators Test ===');
        
        function* numberGenerator() {
            yield 1;
            yield 2;
            yield 3;
            return 'done';
        }
        
        const gen = numberGenerator();
        console.log('Generator next():', gen.next());
        console.log('Generator next():', gen.next());
        console.log('Generator next():', gen.next());
        console.log('Generator next():', gen.next());
        
        // Infinite generator
        function* fibonacci() {
            let [a, b] = [0, 1];
            while (true) {
                yield a;
                [a, b] = [b, a + b];
            }
        }
        
        const fib = fibonacci();
        console.log('Fibonacci sequence:');
        for (let i = 0; i < 10; i++) {
            console.log('Fib:', fib.next().value);
        }
    },

    // // 12. Modules (simulated)
    // testModules: function() {
    //     console.log('\n=== 12. Modules Test ===');
        
    //     // Simulating module patterns since we're in ExtJS context
    //     const MathModule = {
    //         PI: 3.14159,
    //         square: (x) => x * x,
    //         cube: (x) => x * x * x,
    //         default: function(x) {
    //             return x * 2;
    //         }
    //     };
        
    //     // Simulating import destructuring
    //     const { PI, square, cube } = MathModule;
    //     const double = MathModule.default;
        
    //     console.log('Module PI:', PI);
    //     console.log('Module square(5):', square(5));
    //     console.log('Module cube(3):', cube(3));
    //     console.log('Module default(7):', double(7));
    // },

    // 13. Set
    testSet: function() {
        console.log('\n=== 13. Set Test ===');
        
        const mySet = new Set();
        mySet.add(1);
        mySet.add(2);
        mySet.add(2); // Duplicate, won't be added
        mySet.add('hello');
        
        console.log('Set size:', mySet.size);
        console.log('Set has 2:', mySet.has(2));
        console.log('Set values:');
        
        for (const value of mySet) {
            console.log('Set value:', value);
        }
        
        // Set operations
        const set1 = new Set([1, 2, 3]);
        const set2 = new Set([3, 4, 5]);
        
        const union = new Set([...set1, ...set2]);
        const intersection = new Set([...set1].filter(x => set2.has(x)));
        
        console.log('Union:', [...union]);
        console.log('Intersection:', [...intersection]);
    },

    // 14. WeakSet
    testWeakSet: function() {
        console.log('\n=== 14. WeakSet Test ===');
        
        const weakSet = new WeakSet();
        const obj1 = { name: 'Object 1' };
        const obj2 = { name: 'Object 2' };
        
        weakSet.add(obj1);
        weakSet.add(obj2);
        
        console.log('WeakSet has obj1:', weakSet.has(obj1));
        console.log('WeakSet has obj2:', weakSet.has(obj2));
        
        weakSet.delete(obj1);
        console.log('After delete, WeakSet has obj1:', weakSet.has(obj1));
        
        // Note: WeakSet is not enumerable
        console.log('WeakSet is not enumerable and holds weak references');
    },

    // 15. Map
    testMap: function() {
        console.log('\n=== 15. Map Test ===');
        
        const myMap = new Map();
        const keyObject = { id: 1 };
        const keyFunction = function() {};
        
        myMap.set('string', 'value for string key');
        myMap.set(42, 'value for number key');
        myMap.set(keyObject, 'value for object key');
        myMap.set(keyFunction, 'value for function key');
        
        console.log('Map size:', myMap.size);
        console.log('String key:', myMap.get('string'));
        console.log('Number key:', myMap.get(42));
        console.log('Object key:', myMap.get(keyObject));
        
        // Iterate over Map
        console.log('Map entries:');
        for (const [key, value] of myMap) {
            console.log('Key:', key, 'Value:', value);
        }
    },

    // 16. WeakMap
    testWeakMap: function() {
        console.log('\n=== 16. WeakMap Test ===');
        
        const weakMap = new WeakMap();
        const obj1 = { name: 'Key Object 1' };
        const obj2 = { name: 'Key Object 2' };
        
        weakMap.set(obj1, 'Value for obj1');
        weakMap.set(obj2, 'Value for obj2');
        
        console.log('WeakMap get obj1:', weakMap.get(obj1));
        console.log('WeakMap has obj2:', weakMap.has(obj2));
        
        weakMap.delete(obj1);
        console.log('After delete, WeakMap has obj1:', weakMap.has(obj1));
        
        console.log('WeakMap holds weak references and is not enumerable');
    },

    // 17. Unicode
    testUnicode: function() {
        console.log('\n=== 17. Unicode Test ===');
        
        // Unicode code point escapes
        const heart = '\u{1F49C}'; // 💜
        const smiley = '\u{1F60A}'; // 😊
        
        console.log('Unicode hearts:', heart, smiley);
        console.log('String length:', '𝒳𝒴𝒵'.length); // May show unexpected results
        console.log('Code point length:', [...'𝒳𝒴𝒵'].length); // Correct length
        
        // Unicode normalization
        const str1 = 'café';
        const str2 = 'cafe\u0301';
        console.log('Strings equal:', str1 === str2);
        console.log('Normalized equal:', str1.normalize() === str2.normalize());
        
        // String iteration with Unicode
        console.log('Unicode iteration:');
        for (const char of '🌟⭐✨') {
            console.log('Char:', char);
        }
    },

    // 18. Proxies
    testProxies: function() {
        console.log('\n=== 18. Proxies Test ===');
        
        const target = {
            name: 'Original Object',
            value: 42
        };
        
        const handler = {
            get(obj, prop) {
                console.log(`Getting property: ${prop}`);
                return prop in obj ? obj[prop] : `Property ${prop} doesn't exist`;
            },
            set(obj, prop, value) {
                console.log(`Setting ${prop} = ${value}`);
                obj[prop] = value;
                return true;
            }
        };
        
        const proxy = new Proxy(target, handler);
        
        console.log('Proxy get name:', proxy.name);
        console.log('Proxy get nonexistent:', proxy.nonexistent);
        proxy.newProperty = 'New Value';
        console.log('Target after proxy set:', target);
    },

    // 19. Symbols
    testSymbols: function() {
        console.log('\n=== 19. Symbols Test ===');
        
        // Creating symbols
        const sym1 = Symbol();
        const sym2 = Symbol('description');
        const sym3 = Symbol('description');
        
        console.log('Symbol equality:', sym2 === sym3); // false
        console.log('Symbol description:', sym2.toString());
        
        // Symbol as object key
        const obj = {};
        const symKey = Symbol('privateKey');
        obj[symKey] = 'private value';
        obj.publicKey = 'public value';
        
        console.log('Object keys:', Object.keys(obj)); // Won't include symbol
        console.log('Symbol value:', obj[symKey]);
        
        // Well-known symbols
        const iterableObj = {
            data: [1, 2, 3],
            [Symbol.iterator]() {
                let index = 0;
                return {
                    next: () => {
                        if (index < this.data.length) {
                            return { value: this.data[index++], done: false };
                        }
                        return { done: true };
                    }
                };
            }
        };
        
        console.log('Symbol.iterator example:', [...iterableObj]);
    },

    // 20. Promises
    testPromises: function() {
        console.log('\n=== 20. Promises Test ===');
        
        // Basic Promise
        const promise1 = new Promise((resolve, reject) => {
            setTimeout(() => {
                resolve('Promise resolved!');
            }, 100);
        });
        
        promise1.then(result => {
            console.log('Promise result:', result);
        });
        
        // Promise chaining
        Promise.resolve(5)
            .then(x => x * 2)
            .then(x => x + 3)
            .then(result => {
                console.log('Promise chain result:', result);
            });
        
        // Promise.all
        const promises = [
            Promise.resolve(1),
            Promise.resolve(2),
            Promise.resolve(3)
        ];
        
        Promise.all(promises).then(results => {
            console.log('Promise.all results:', results);
        });
        
        // Error handling
        Promise.reject('Error occurred')
            .catch(error => {
                console.log('Promise error caught:', error);
            });
    },

    // 21. Reflect
    testReflect: function() {
        console.log('\n=== 21. Reflect Test ===');
        
        const obj = {
            name: 'Test Object',
            getValue() {
                return this.name;
            }
        };
        
        // Reflect methods
        console.log('Reflect.has:', Reflect.has(obj, 'name'));
        console.log('Reflect.get:', Reflect.get(obj, 'name'));
        
        Reflect.set(obj, 'age', 25);
        console.log('After Reflect.set:', obj);
        
        const keys = Reflect.ownKeys(obj);
        console.log('Reflect.ownKeys:', keys);
        
        // Reflect with Proxy
        const handler = {
            get(target, prop, receiver) {
                console.log(`Reflect proxy getting: ${prop}`);
                return Reflect.get(target, prop, receiver);
            }
        };
        
        const proxy = new Proxy(obj, handler);
        console.log('Reflect with proxy:', proxy.name);
    },

    // 22. Binary and Octal
    testBinaryOctal: function() {
        console.log('\n=== 22. Binary and Octal Test ===');
        
        // Binary literals
        const binary = 0b1010; // 10 in decimal
        console.log('Binary 0b1010:', binary);
        
        // Octal literals
        const octal = 0o755; // 493 in decimal
        console.log('Octal 0o755:', octal);
        
        // Convert to different bases
        const number = 42;
        console.log('Number 42 in binary:', number.toString(2));
        console.log('Number 42 in octal:', number.toString(8));
        console.log('Number 42 in hex:', number.toString(16));
        
        // Parsing from strings
        console.log('Parse binary:', parseInt('1010', 2));
        console.log('Parse octal:', parseInt('755', 8));
        console.log('Parse hex:', parseInt('2A', 16));
    },

    // 23. Proper Tail Calls (optimization feature)
    testProperTailCalls: function() {
        console.log('\n=== 23. Proper Tail Calls Test ===');
        
        // Tail call optimized function
        function factorial(n, acc = 1) {
            if (n <= 1) return acc;
            return factorial(n - 1, n * acc); // Tail call
        }
        
        console.log('Factorial with tail call:', factorial(5));
        
        // Non-tail call version for comparison
        function factorialNonTail(n) {
            if (n <= 1) return 1;
            return n * factorialNonTail(n - 1); // Not a tail call
        }
        
        console.log('Factorial non-tail call:', factorialNonTail(5));
        
        console.log('Note: Tail call optimization depends on strict mode and engine support');
    },

    // 24. Array Find Methods
    testArrayFindMethods: function() {
        console.log('\n=== 24. Array Find Methods Test ===');
        
        const numbers = [1, 5, 10, 15, 20, 25];
        const people = [
            { name: 'Alice', age: 25 },
            { name: 'Bob', age: 30 },
            { name: 'Charlie', age: 35 }
        ];
        
        // Array.find()
        const found = numbers.find(num => num > 10);
        console.log('Array.find (>10):', found);
        
        const person = people.find(p => p.age === 30);
        console.log('Array.find person:', person);
        
        // Array.findIndex()
        const foundIndex = numbers.findIndex(num => num > 10);
        console.log('Array.findIndex (>10):', foundIndex);
        
        const personIndex = people.findIndex(p => p.name === 'Charlie');
        console.log('Array.findIndex Charlie:', personIndex);
        
        // Not found cases
        const notFound = numbers.find(num => num > 100);
        const notFoundIndex = numbers.findIndex(num => num > 100);
        console.log('Not found value:', notFound); // undefined
        console.log('Not found index:', notFoundIndex); // -1
        
        // Array.includes() (bonus ES7 method)
        if (Array.prototype.includes) {
            console.log('Array.includes(15):', numbers.includes(15));
            console.log('Array.includes(100):', numbers.includes(100));
        }
    },

    // ========================================
    // ES2016/ES7 FEATURES
    // ========================================

    testES2016Features: function() {
        console.log('\n🚀 === ES2016/ES7 FEATURES ===');
        this.testES2016ArrayIncludes();
        this.testES2016ExponentiationOperator();
    },

    testES2016ArrayIncludes: function() {
        console.log('\n=== ES2016: Array.includes ===');
        
        const numbers = [1, 2, 3, NaN, 5];
        const fruits = ['apple', 'banana', 'orange'];
        
        console.log('Array includes 3:', numbers.includes(3));
        console.log('Array includes 4:', numbers.includes(4));
        console.log('Array includes NaN:', numbers.includes(NaN)); // Works with NaN
        console.log('Array includes from index 2:', numbers.includes(1, 2)); // false
        
        console.log('Fruits includes apple:', fruits.includes('apple'));
        console.log('Fruits includes grape:', fruits.includes('grape'));
        
        // Comparison with indexOf
        console.log('indexOf vs includes with NaN:');
        console.log('indexOf NaN:', numbers.indexOf(NaN)); // -1 (doesn't work)
        console.log('includes NaN:', numbers.includes(NaN)); // true (works)
    },

    // testES2016ExponentiationOperator: function() {
    //     console.log('\n=== ES2016: Exponentiation Operator (**) ===');
        
    //     console.log('2 ** 3 =', 2 ** 3);
    //     console.log('2 ** 10 =', 2 ** 10);
    //     console.log('3 ** 4 =', 3 ** 4);
        
    //     // Comparison with Math.pow
    //     console.log('** vs Math.pow:');
    //     console.log('5 ** 2 =', 5 ** 2);
    //     console.log('Math.pow(5, 2) =', Math.pow(5, 2));
        
    //     // With assignment
    //     let base = 2;
    //     base **= 8;
    //     console.log('2 **= 8 result:', base);
        
    //     // Fractional exponents
    //     console.log('9 ** 0.5 (square root):', 9 ** 0.5);
    //     console.log('8 ** (1/3) (cube root):', 8 ** (1/3));
        
    //     // Negative exponents
    //     console.log('2 ** -3 =', 2 ** -3);
    // },

    // // ========================================
    // // ES2017/ES8 FEATURES
    // // ========================================

    testES2017Features: function() {
        console.log('\n🚀 === ES2017/ES8 FEATURES ===');
        this.testES2017AsyncFunctions();
        this.testES2017ObjectValues();
        this.testES2017ObjectEntries();
        this.testES2017ObjectPropertyDescriptors();
        this.testES2017StringPadding();
        this.testES2017TrailingCommas();
    },

    testES2017AsyncFunctions: async function() {
        console.log('\n=== ES2017: Async Functions ===');
        
        // Basic async function
        async function fetchData() {
            return new Promise(resolve => {
                setTimeout(() => resolve('Data fetched!'), 100);
            });
        }
        
        try {
            const result = await fetchData();
            console.log('Async result:', result);
        } catch (error) {
            console.log('Async error:', error);
        }
        
        // Async arrow function
        const asyncArrow = async () => {
            await new Promise(resolve => setTimeout(resolve, 50));
            return 'Arrow async complete';
        };
        
        console.log('Async arrow result:', await asyncArrow());
        
        // Multiple async operations
        const operations = [
            new Promise(resolve => setTimeout(() => resolve('Op 1'), 50)),
            new Promise(resolve => setTimeout(() => resolve('Op 2'), 30)),
            new Promise(resolve => setTimeout(() => resolve('Op 3'), 40))
        ];
        
        const results = await Promise.all(operations);
        console.log('Multiple async results:', results);
        
        // Error handling
        async function maybeError() {
            const random = Math.random();
            if (random > 0.5) {
                throw new Error('Random error occurred');
            }
            return 'Success!';
        }
        
        try {
            const result = await maybeError();
            console.log('Maybe error result:', result);
        } catch (error) {
            console.log('Caught error:', error.message);
        }
    },

    testES2017ObjectValues: function() {
        console.log('\n=== ES2017: Object.values ===');
        
        const person = {
            name: 'John',
            age: 30,
            city: 'New York'
        };
        
        const values = Object.values(person);
        console.log('Object values:', values);
        
        // With array
        const arr = ['a', 'b', 'c'];
        console.log('Array values:', Object.values(arr));
        
        // With string
        console.log('String values:', Object.values('hello'));
        
        // Comparison with Object.keys
        console.log('Object.keys:', Object.keys(person));
        console.log('Object.values:', Object.values(person));
    },

    testES2017ObjectEntries: function() {
        console.log('\n=== ES2017: Object.entries ===');
        
        const config = {
            host: 'localhost',
            port: 3000,
            protocol: 'https'
        };
        
        const entries = Object.entries(config);
        console.log('Object entries:', entries);
        
        // Converting back to object
        const reconstructed = Object.fromEntries(entries);
        console.log('Reconstructed object:', reconstructed);
        
        // Iterating over entries
        console.log('Iterating entries:');
        for (const [key, value] of Object.entries(config)) {
            console.log(`${key}: ${value}`);
        }
        
        // Transforming objects
        const doubled = Object.fromEntries(
            Object.entries({ a: 1, b: 2, c: 3 }).map(([key, value]) => [key, value * 2])
        );
        console.log('Doubled values:', doubled);
    },

    testES2017ObjectPropertyDescriptors: function() {
        console.log('\n=== ES2017: Object.getOwnPropertyDescriptors ===');
        
        const obj = {};
        Object.defineProperty(obj, 'name', {
            value: 'Test Object',
            writable: false,
            enumerable: true,
            configurable: true
        });
        
        Object.defineProperty(obj, 'hidden', {
            value: 'Secret',
            writable: true,
            enumerable: false,
            configurable: true
        });
        
        const descriptors = Object.getOwnPropertyDescriptors(obj);
        console.log('Property descriptors:', descriptors);
        
        // Cloning with descriptors
        const clone = Object.create(
            Object.getPrototypeOf(obj),
            Object.getOwnPropertyDescriptors(obj)
        );
        console.log('Cloned object:', clone);
        console.log('Clone name:', clone.name);
        
        // Getter and setter
        const objWithAccessor = {
            _value: 0,
            get value() { return this._value; },
            set value(v) { this._value = v * 2; }
        };
        
        const accessorDescriptors = Object.getOwnPropertyDescriptors(objWithAccessor);
        console.log('Accessor descriptors:', accessorDescriptors.value);
    },

    testES2017StringPadding: function() {
        console.log('\n=== ES2017: String Padding ===');
        
        const str = 'hello';
        
        // padStart
        console.log('padStart(10):', str.padStart(10));
        console.log('padStart(10, "."):', str.padStart(10, '.'));
        console.log('padStart(3):', str.padStart(3)); // No padding needed
        
        // padEnd
        console.log('padEnd(10):', str.padEnd(10));
        console.log('padEnd(10, "!"):', str.padEnd(10, '!'));
        console.log('padEnd(3):', str.padEnd(3)); // No padding needed
        
        // Practical examples
        const numbers = [1, 22, 333, 4444];
        console.log('Aligned numbers:');
        numbers.forEach(num => {
            console.log(num.toString().padStart(6, '0'));
        });
        
        // Creating a simple table
        const data = [
            { name: 'Alice', score: 95 },
            { name: 'Bob', score: 87 },
            { name: 'Charlie', score: 92 }
        ];
        
        console.log('Simple table:');
        data.forEach(row => {
            console.log(`${row.name.padEnd(10)} ${row.score.toString().padStart(3)}`);
        });
    },

    testES2017TrailingCommas: function() {
        console.log('\n=== ES2017: Trailing Commas ===');
        
        // Arrays with trailing commas
        const fruits = [
            'apple',
            'banana',
            'orange', // Trailing comma allowed
        ];
        console.log('Array with trailing comma:', fruits);
        
        // Objects with trailing commas
        const person = {
            name: 'John',
            age: 30,
            city: 'New York', // Trailing comma allowed
        };
        console.log('Object with trailing comma:', person);
        
        // Function parameters with trailing commas
        function greet(
            name,
            greeting,
            punctuation, // Trailing comma allowed in parameters
        ) {
            return `${greeting}, ${name}${punctuation}`;
        }
        
        console.log('Function with trailing comma:', greet('World', 'Hello', '!'));
        
        // Function calls with trailing commas
        const result = Math.max(
            1,
            2,
            3, // Trailing comma allowed in calls
        );
        console.log('Function call with trailing comma:', result);
    },

    // ========================================
    // ES2018/ES9 FEATURES
    // ========================================

    testES2018Features: function() {
        console.log('\n🚀 === ES2018/ES9 FEATURES ===');
        this.testES2018AsyncIterators();
        this.testES2018ObjectRestSpread();
        this.testES2018PromiseFinally();
    },

    testES2018AsyncIterators: async function() {
        console.log('\n=== ES2018: Async Iterators ===');
        
        // Async generator
        async function* asyncGenerator() {
            yield await Promise.resolve(1);
            yield await Promise.resolve(2);
            yield await Promise.resolve(3);
        }
        
        console.log('Async generator results:');
        for await (const value of asyncGenerator()) {
            console.log('Async value:', value);
        }
        
        // Custom async iterable
        const asyncIterable = {
            async *[Symbol.asyncIterator]() {
                for (let i = 1; i <= 3; i++) {
                    await new Promise(resolve => setTimeout(resolve, 50));
                    yield `Item ${i}`;
                }
            }
        };
        
        console.log('Custom async iterable:');
        for await (const item of asyncIterable) {
            console.log('Async item:', item);
        }
        
        // Async iteration with error handling
        async function* errorGenerator() {
            yield 'First';
            yield 'Second';
            throw new Error('Async error');
        }
        
        try {
            for await (const value of errorGenerator()) {
                console.log('Error gen value:', value);
            }
        } catch (error) {
            console.log('Caught async error:', error.message);
        }
    },

    testES2018ObjectRestSpread: function() {
        console.log('\n=== ES2018: Object Rest/Spread ===');
        
        const person = {
            name: 'John',
            age: 30,
            city: 'New York',
            country: 'USA'
        };
        
        // Object destructuring with rest
        const { name, age, ...rest } = person;
        console.log('Name:', name);
        console.log('Age:', age);
        console.log('Rest:', rest);
        
        // Object spread
        const basicInfo = { name: 'Alice', age: 25 };
        const additionalInfo = { city: 'Boston', job: 'Developer' };
        
        const fullInfo = { ...basicInfo, ...additionalInfo };
        console.log('Spread combined:', fullInfo);
        
        // Overriding properties
        const original = { a: 1, b: 2, c: 3 };
        const modified = { ...original, b: 20, d: 4 };
        console.log('Override with spread:', modified);
        
        // Nested object spread (shallow)
        const nested = {
            user: { name: 'Bob', age: 28 },
            settings: { theme: 'dark', notifications: true }
        };
        
        const nestedSpread = { ...nested, user: { ...nested.user, age: 29 } };
        console.log('Nested spread:', nestedSpread);
        
        // Function parameters with object rest
        function processUser({ name, ...otherDetails }) {
            console.log('Processing user:', name);
            console.log('Other details:', otherDetails);
        }
        
        processUser({ name: 'Charlie', age: 35, city: 'Seattle', job: 'Designer' });
    },

    testES2018PromiseFinally: function() {
        console.log('\n=== ES2018: Promise.finally ===');
        
        let isLoading = true;
        
        const simulateRequest = (shouldSucceed) => {
            return new Promise((resolve, reject) => {
                setTimeout(() => {
                    if (shouldSucceed) {
                        resolve('Request successful');
                    } else {
                        reject(new Error('Request failed'));
                    }
                }, 100);
            });
        };
        
        // Successful promise with finally
        simulateRequest(true)
            .then(result => {
                console.log('Success:', result);
            })
            .catch(error => {
                console.log('Error:', error.message);
            })
            .finally(() => {
                isLoading = false;
                console.log('Finally: Loading complete (success case)');
            });
        
        // Failed promise with finally
        setTimeout(() => {
            simulateRequest(false)
                .then(result => {
                    console.log('Success:', result);
                })
                .catch(error => {
                    console.log('Error:', error.message);
                })
                .finally(() => {
                    console.log('Finally: Cleanup complete (error case)');
                });
        }, 200);
        
        // Finally with async/await
        setTimeout(async () => {
            try {
                const result = await simulateRequest(true);
                console.log('Async success:', result);
            } catch (error) {
                console.log('Async error:', error.message);
            } finally {
                console.log('Finally: Async cleanup complete');
            }
        }, 300);
    },

    // ========================================
    // ES2019/ES10 FEATURES
    // ========================================

    testES2019Features: function() {
        console.log('\n🚀 === ES2019/ES10 FEATURES ===');
        this.testES2019ArrayFlat();
        this.testES2019ObjectFromEntries();
        this.testES2019StringTrim();
        this.testES2019SymbolDescription();
        this.testES2019OptionalCatch();
        this.testES2019ArrayStableSort();
    },

    testES2019ArrayFlat: function() {
        console.log('\n=== ES2019: Array.flat & flatMap ===');
        
        // Array.flat()
        const nested = [1, [2, 3], [4, [5, 6]]];
        console.log('Original nested:', nested);
        console.log('Flat (depth 1):', nested.flat());
        console.log('Flat (depth 2):', nested.flat(2));
        console.log('Flat (Infinity):', nested.flat(Infinity));
        
        // Array.flatMap()
        const numbers = [1, 2, 3, 4];
        const flatMapped = numbers.flatMap(x => [x, x * 2]);
        console.log('FlatMap result:', flatMapped);
        
        // Practical flatMap example
        const sentences = ['Hello world', 'How are you', 'Fine thanks'];
        const words = sentences.flatMap(sentence => sentence.split(' '));
        console.log('Words from sentences:', words);
        
        // FlatMap vs map + flat
        const mapThenFlat = numbers.map(x => [x, x * 2]).flat();
        console.log('Map then flat:', mapThenFlat);
        console.log('Same as flatMap:', JSON.stringify(flatMapped) === JSON.stringify(mapThenFlat));
        
        // Complex nested structure
        const complex = [1, [2, [3, [4, [5]]]]];
        console.log('Complex nested:', complex);
        console.log('Completely flattened:', complex.flat(Infinity));
    },

    testES2019ObjectFromEntries: function() {
        console.log('\n=== ES2019: Object.fromEntries ===');
        
        // Basic usage
        const entries = [['name', 'John'], ['age', 30], ['city', 'Boston']];
        const obj = Object.fromEntries(entries);
        console.log('From entries:', obj);
        
        // Round trip with Object.entries
        const original = { a: 1, b: 2, c: 3 };
        const roundTrip = Object.fromEntries(Object.entries(original));
        console.log('Round trip:', roundTrip);
        
        // Transforming objects
        const scores = { math: 95, science: 87, english: 92 };
        const normalized = Object.fromEntries(
            Object.entries(scores).map(([subject, score]) => [subject, score / 100])
        );
        console.log('Normalized scores:', normalized);
        
        // From Map
        const map = new Map([['x', 10], ['y', 20], ['z', 30]]);
        const fromMap = Object.fromEntries(map);
        console.log('From Map:', fromMap);
        
        // Filtering object properties
        const user = { name: 'Alice', age: 25, password: 'secret', email: '<EMAIL>' };
        const publicInfo = Object.fromEntries(
            Object.entries(user).filter(([key]) => key !== 'password')
        );
        console.log('Filtered object:', publicInfo);
    },

    testES2019StringTrim: function() {
        console.log('\n=== ES2019: String trimStart & trimEnd ===');
        
        const str = '   Hello World   ';
        console.log('Original: "' + str + '"');
        console.log('trimStart(): "' + str.trimStart() + '"');
        console.log('trimEnd(): "' + str.trimEnd() + '"');
        console.log('trim(): "' + str.trim() + '"');
        
        // Aliases
        console.log('trimLeft(): "' + str.trimLeft() + '"');
        console.log('trimRight(): "' + str.trimRight() + '"');
        
        // Different whitespace characters
        const whitespace = '\t\n  Hello  \r\n\t';
        console.log('Whitespace original: "' + whitespace + '"');
        console.log('Whitespace trimStart: "' + whitespace.trimStart() + '"');
        console.log('Whitespace trimEnd: "' + whitespace.trimEnd() + '"');
        
        // Practical use case
        const userInput = '  <EMAIL>  ';
        const cleaned = userInput.trimStart().trimEnd();
        console.log('Cleaned email: "' + cleaned + '"');
    },

    testES2019SymbolDescription: function() {
        console.log('\n=== ES2019: Symbol.description ===');
        
        const sym1 = Symbol('my symbol');
        const sym2 = Symbol();
        
        console.log('Symbol with description:', sym1.toString());
        console.log('Symbol description property:', sym1.description);
        console.log('Symbol without description:', sym2.description);
        
        // Comparison with toString
        console.log('toString():', sym1.toString());
        console.log('description:', sym1.description);
        
        // Symbol description is read-only
        const sym3 = Symbol('test');
        console.log('Original description:', sym3.description);
        // sym3.description = 'changed'; // Would throw in strict mode
        console.log('Description after attempt to change:', sym3.description);
        
        // Well-known symbols
        console.log('Iterator symbol description:', Symbol.iterator.description);
        console.log('AsyncIterator description:', Symbol.asyncIterator.description);
    },

    // testES2019OptionalCatch: function() {
    //     console.log('\n=== ES2019: Optional Catch Binding ===');
        
    //     // Traditional catch with parameter
    //     try {
    //         throw new Error('Traditional error');
    //     } catch (error) {
    //         console.log('Traditional catch:', error.message);
    //     }
        
    //     // Optional catch without parameter
    //     try {
    //         JSON.parse('invalid json');
    //     } catch {
    //         console.log('Optional catch: JSON parsing failed');
    //     }
        
    //     // Practical example - feature detection
    //     let hasFeature = false;
    //     try {
    //         // Test if a feature exists
    //         eval('new URLSearchParams()');
    //         hasFeature = true;
    //     } catch {
    //         hasFeature = false;
    //     }
    //     console.log('Feature detection result:', hasFeature);
        
    //     // Another practical example
    //     function safeParseInt(str) {
    //         try {
    //             return parseInt(str);
    //         } catch {
    //             return 0;
    //         }
    //     }
        
    //     console.log('Safe parse int:', safeParseInt('123'));
    //     console.log('Safe parse int (invalid):', safeParseInt('abc'));
    // },

    testES2019ArrayStableSort: function() {
        console.log('\n=== ES2019: Array Stable Sort ===');
        
        const students = [
            { name: 'Alice', grade: 85, id: 1 },
            { name: 'Bob', grade: 90, id: 2 },
            { name: 'Charlie', grade: 85, id: 3 },
            { name: 'David', grade: 90, id: 4 },
            { name: 'Eve', grade: 85, id: 5 }
        ];
        
        console.log('Original order:', students.map(s => `${s.name}(${s.grade})`));
        
        // Sort by grade (stable sort maintains original order for equal elements)
        const sortedByGrade = [...students].sort((a, b) => a.grade - b.grade);
        console.log('Sorted by grade:', sortedByGrade.map(s => `${s.name}(${s.grade})`));
        
        // Demonstrating stability
        const items = [
            { value: 3, original: 'first' },
            { value: 1, original: 'A' },
            { value: 3, original: 'second' },
            { value: 2, original: 'B' },
            { value: 3, original: 'third' }
        ];
        
        console.log('Items before sort:', items.map(i => `${i.value}-${i.original}`));
        
        const stableSorted = [...items].sort((a, b) => a.value - b.value);
        console.log('Stable sorted:', stableSorted.map(i => `${i.value}-${i.original}`));
        console.log('Note: Items with value 3 maintain their original relative order');
    },

    // // ========================================
    // // ES2020/ES11 FEATURES
    // // ========================================

    testES2020Features: function() {
        console.log('\n🚀 === ES2020/ES11 FEATURES ===');
        // this.testES2020BigInt();
        this.testES2020NullishCoalescing();
        this.testES2020OptionalChaining();
        this.testES2020PromiseAllSettled();
        this.testES2020StringMatchAll();
        this.testES2020GlobalThis();
    },

    // testES2020BigInt: function() {
    //     console.log('\n=== ES2020: BigInt ===');
        
    //     // Creating BigInts
    //     const bigInt1 = BigInt(123);
    //     const bigInt2 = 456n;
    //     const bigInt3 = BigInt('789');
        
    //     console.log('BigInt from number:', bigInt1);
    //     console.log('BigInt literal:', bigInt2);
    //     console.log('BigInt from string:', bigInt3);
        
    //     // Large numbers
    //     const largeNumber = 9007199254740991n; // MAX_SAFE_INTEGER + 1
    //     console.log('Large BigInt:', largeNumber);
    //     console.log('Larger BigInt:', largeNumber * 2n);
        
    //     // BigInt operations
    //     console.log('BigInt addition:', 10n + 20n);
    //     console.log('BigInt multiplication:', 5n * 6n);
    //     console.log('BigInt exponentiation:', 2n ** 100n);
        
    //     // Type checking
    //     console.log('typeof 42n:', typeof 42n);
    //     console.log('42n === 42:', 42n === 42); // false
    //     console.log('42n == 42:', 42n == 42); // true
        
    //     // Cannot mix BigInt with regular numbers
    //     try {
    //         console.log(10n + 5); // This will throw
    //     } catch (error) {
    //         console.log('Cannot mix BigInt with Number:', error.message);
    //     }
        
    //     // Converting between BigInt and Number
    //     const num = 42;
    //     const big = BigInt(num);
    //     const backToNum = Number(big);
    //     console.log('Number to BigInt to Number:', num, '=>', big, '=>', backToNum);
    // },

    testES2020NullishCoalescing: function() {
        console.log('\n=== ES2020: Nullish Coalescing (??) ===');
        
        // Basic usage
        const name = null ?? 'Default Name';
        const age = undefined ?? 25;
        const score = 0 ?? 100; // 0 is not nullish
        
        console.log('Null coalescing name:', name);
        console.log('Undefined coalescing age:', age);
        console.log('Zero coalescing score:', score);
        
        // Comparison with ||
        const falsyValues = [null, undefined, 0, '', false, NaN];
        
        console.log('Comparison: ?? vs ||');
        falsyValues.forEach(value => {
            console.log(`${value} ?? "default":`, value ?? 'default');
            console.log(`${value} || "default":`, value || 'default');
        });
        
        // Practical example
        function createUser(options = {}) {
            return {
                name: options.name ?? 'Anonymous',
                age: options.age ?? 18,
                active: options.active ?? true,
                score: options.score ?? 0 // Want to keep 0 as valid score
            };
        }
        
        console.log('User with nullish values:', createUser({ name: null, score: 0 }));
        console.log('User with falsy values:', createUser({ name: '', score: 0 }));
        
        // Chaining with nullish coalescing
        const config = {
            api: {
                timeout: null
            }
        };
        
        const timeout = config?.api?.timeout ?? 5000;
        console.log('Chained nullish coalescing:', timeout);
    },

    testES2020OptionalChaining: function() {
        console.log('\n=== ES2020: Optional Chaining (?.) ===');
        
        const user = {
            name: 'John',
            address: {
                street: '123 Main St',
                city: 'Boston'
            },
            getEmail: function() {
                return '<EMAIL>';
            }
        };
        
        const userWithoutAddress = {
            name: 'Jane'
        };
        
        // Property access
        console.log('User city:', user?.address?.city);
        console.log('User without address city:', userWithoutAddress?.address?.city);
        console.log('Deep nesting:', user?.address?.coordinates?.lat);
        
        // Method calls
        console.log('User email:', user?.getEmail?.());
        console.log('Non-existent method:', user?.getNonExistent?.());
        
        // Array access
        const users = [user, userWithoutAddress];
        console.log('First user name:', users?.[0]?.name);
        console.log('Non-existent user:', users?.[10]?.name);
        
        // Dynamic property access
        const propertyName = 'address';
        console.log('Dynamic access:', user?.[propertyName]?.city);
        
        // Practical example with API response
        const apiResponse = {
            data: {
                users: [
                    { id: 1, profile: { avatar: 'avatar1.jpg' } },
                    { id: 2 } // No profile
                ]
            }
        };
        
        console.log('First user avatar:', apiResponse?.data?.users?.[0]?.profile?.avatar);
        console.log('Second user avatar:', apiResponse?.data?.users?.[1]?.profile?.avatar);
        
        // Optional chaining with function calls
        const calculator = {
            operations: {
                add: (a, b) => a + b,
                multiply: (a, b) => a * b
            }
        };
        
        console.log('Calculator add:', calculator?.operations?.add?.(5, 3));
        console.log('Calculator divide:', calculator?.operations?.divide?.(10, 2));
    },

    testES2020PromiseAllSettled: function() {
        console.log('\n=== ES2020: Promise.allSettled ===');
        
        const promises = [
            Promise.resolve('Success 1'),
            Promise.reject('Error 1'),
            Promise.resolve('Success 2'),
            Promise.reject('Error 2'),
            new Promise(resolve => setTimeout(() => resolve('Delayed success'), 100))
        ];
        
        Promise.allSettled(promises).then(results => {
            console.log('All settled results:');
            results.forEach((result, index) => {
                if (result.status === 'fulfilled') {
                    console.log(`Promise ${index}: fulfilled with`, result.value);
                } else {
                    console.log(`Promise ${index}: rejected with`, result.reason);
                }
            });
            
            // Count successful vs failed
            const successful = results.filter(r => r.status === 'fulfilled').length;
            const failed = results.filter(r => r.status === 'rejected').length;
            console.log(`Summary: ${successful} successful, ${failed} failed`);
        });
        
        // Comparison with Promise.all
        setTimeout(() => {
            Promise.all([Promise.resolve(1), Promise.reject('error'), Promise.resolve(3)])
                .then(results => {
                    console.log('Promise.all results:', results);
                })
                .catch(error => {
                    console.log('Promise.all caught error:', error);
                });
        }, 200);
    },

    testES2020StringMatchAll: function() {
        console.log('\n=== ES2020: String.matchAll ===');
        
        const text = 'The year 2020 was followed by 2021 and then 2022';
        const yearRegex = /\d{4}/g;
        
        // Using matchAll
        const matches = [...text.matchAll(yearRegex)];
        console.log('All year matches:', matches.map(match => match[0]));
        
        // Detailed match information
        matches.forEach((match, index) => {
            console.log(`Match ${index}:`, {
                value: match[0],
                index: match.index,
                input: match.input.substring(match.index - 5, match.index + 10)
            });
        });
        
        // With capture groups
        const dateText = 'Today is 2024-06-13 and tomorrow is 2024-06-14';
        const dateRegex = /(\d{4})-(\d{2})-(\d{2})/g;
        
        console.log('Date matches with groups:');
        for (const match of dateText.matchAll(dateRegex)) {
            console.log('Full match:', match[0]);
            console.log('Year:', match[1], 'Month:', match[2], 'Day:', match[3]);
            console.log('---');
        }
        
        // Comparison with match() and exec()
        console.log('Comparison with other methods:');
        console.log('match() with global:', text.match(yearRegex));
        
        let execMatch;
        const execResults = [];
        while ((execMatch = yearRegex.exec(text)) !== null) {
            execResults.push(execMatch[0]);
        }
        console.log('exec() loop results:', execResults);
    },

    testES2020GlobalThis: function() {
        console.log('\n=== ES2020: globalThis ===');
        
        // globalThis provides a standardized way to access the global object
        console.log('globalThis type:', typeof globalThis);
        
        // In browser: globalThis === window
        // In Node.js: globalThis === global
        // In Web Workers: globalThis === self
        
        // Setting a global variable
        globalThis.myGlobalVar = 'Hello from globalThis';
        console.log('Global variable:', globalThis.myGlobalVar);
        
        // Feature detection before globalThis
        function getGlobalObject() {
            if (typeof globalThis !== 'undefined') return globalThis;
            if (typeof window !== 'undefined') return window;
            if (typeof global !== 'undefined') return global;
            if (typeof self !== 'undefined') return self;
            throw new Error('Unable to locate global object');
        }
        
        const globalObj = getGlobalObject();
        console.log('Global object found:', typeof globalObj);
        
        // Polyfill example (educational)
        if (!globalThis) {
            (function() {
                if (typeof window !== 'undefined') {
                    window.globalThis = window;
                } else if (typeof global !== 'undefined') {
                    global.globalThis = global;
                } else if (typeof self !== 'undefined') {
                    self.globalThis = self;
                }
            })();
        }
        
        console.log('globalThis standardizes global object access across environments');
    },

    // ========================================
    // ES2021/ES12 FEATURES
    // ========================================

    testES2021Features: function() {
        console.log('\n🚀 === ES2021/ES12 FEATURES ===');
        this.testES2021ReplaceAll();
        this.testES2021PromiseAny();
        this.testES2021WeakRef();
        this.testES2021NumericSeparators();
        this.testES2021LogicalAssignment();
    },

    testES2021ReplaceAll: function() {
        console.log('\n=== ES2021: String.replaceAll ===');
        
        const text = 'The quick brown fox jumps over the lazy dog. The fox is quick.';
        
        // Basic replaceAll
        const replaced = text.replaceAll('the', 'THE');
        console.log('Replace all "the":', replaced);
        
        // Case sensitive
        const caseSensitive = text.replaceAll('The', 'A');
        console.log('Replace "The":', caseSensitive);
        
        // With regex (must have global flag)
        const regexReplace = text.replaceAll(/fox/g, 'cat');
        console.log('Regex replace:', regexReplace);
        
        // Comparison with replace()
        console.log('replace() only first:', text.replace('the', 'THE'));
        console.log('replaceAll() all:', text.replaceAll('the', 'THE'));
        
        // Practical examples
        const csv = 'name,age,city\nJohn,30,Boston\nJane,25,Seattle';
        const tsv = csv.replaceAll(',', '\t');
        console.log('CSV to TSV:\n', tsv);
        
        // HTML sanitization example
        const userInput = '<script>alert("xss")</script>Hello <b>world</b>';
        const sanitized = userInput.replaceAll('<', '&lt;').replaceAll('>', '&gt;');
        console.log('Sanitized HTML:', sanitized);
        
        // URL parameter cleaning
        const url = 'https://example.com?param1=value1&param2=value2&param1=value3';
        const cleanUrl = url.replaceAll('&param1=value1', '');
        console.log('Cleaned URL:', cleanUrl);
    },

    testES2021PromiseAny: function() {
        console.log('\n=== ES2021: Promise.any ===');
        
        const fastPromise = new Promise(resolve => setTimeout(() => resolve('Fast'), 50));
        const slowPromise = new Promise(resolve => setTimeout(() => resolve('Slow'), 200));
        const failingPromise = Promise.reject('Failed');
        
        // Promise.any - resolves with first fulfilled promise
        Promise.any([failingPromise, slowPromise, fastPromise])
            .then(result => {
                console.log('Promise.any result:', result);
            })
            .catch(error => {
                console.log('Promise.any error:', error);
            });
        
        // All promises reject - AggregateError
        const allFailing = [
            Promise.reject('Error 1'),
            Promise.reject('Error 2'),
            Promise.reject('Error 3')
        ];
        
        Promise.any(allFailing)
            .then(result => {
                console.log('All failing result:', result);
            })
            .catch(error => {
                console.log('AggregateError:', error.constructor.name);
                console.log('All errors:', error.errors);
            });
        
        // Comparison with other Promise methods
        setTimeout(() => {
            const promises = [
                Promise.reject('Reject 1'),
                Promise.resolve('Resolve 1'),
                Promise.resolve('Resolve 2')
            ];
            
            console.log('\nComparison of Promise methods:');
            
            Promise.any(promises)
                .then(result => console.log('Promise.any:', result))
                .catch(error => console.log('Promise.any error:', error));
            
            Promise.race(promises)
                .then(result => console.log('Promise.race:', result))
                .catch(error => console.log('Promise.race error:', error));
            
            Promise.all(promises)
                .then(result => console.log('Promise.all:', result))
                .catch(error => console.log('Promise.all error:', error));
            
            Promise.allSettled(promises)
                .then(results => console.log('Promise.allSettled:', results.map(r => r.status)));
        }, 300);
    },

    testES2021WeakRef: function() {
        console.log('\n=== ES2021: WeakRef ===');
        
        // Note: WeakRef should be used sparingly and with caution
        let target = { name: 'Target Object', data: new Array(1000).fill('data') };
        
        // Create a weak reference
        const weakRef = new WeakRef(target);
        
        console.log('WeakRef created');
        console.log('Target via WeakRef:', weakRef.deref()?.name);
        
        // Function to check if target is still alive
        function checkTarget() {
            const obj = weakRef.deref();
            if (obj) {
                console.log('Target still alive:', obj.name);
                return true;
            } else {
                console.log('Target has been garbage collected');
                return false;
            }
        }
        
        // Target is still strongly referenced
        checkTarget();
        
        // Remove strong reference
        target = null;
        
        // Force garbage collection (if available)
        if (typeof gc === 'function') {
            gc();
        }
        
        // Check again after potential GC
        setTimeout(() => {
            checkTarget();
        }, 100);
        
        // Practical example - cache with weak references
        class WeakCache {
            constructor() {
                this.cache = new Map();
            }
            
            set(key, value) {
                this.cache.set(key, new WeakRef(value));
            }
            
            get(key) {
                const ref = this.cache.get(key);
                if (ref) {
                    const value = ref.deref();
                    if (value) {
                        return value;
                    } else {
                        // Clean up dead reference
                        this.cache.delete(key);
                    }
                }
                return undefined;
            }
            
            cleanup() {
                for (const [key, ref] of this.cache) {
                    if (!ref.deref()) {
                        this.cache.delete(key);
                    }
                }
            }
        }
        
        const cache = new WeakCache();
        let obj1 = { id: 1, data: 'Important data' };
        
        cache.set('obj1', obj1);
        console.log('Cached object:', cache.get('obj1'));
        
        obj1 = null; // Remove strong reference
        
        setTimeout(() => {
            console.log('After removing strong reference:', cache.get('obj1'));
            cache.cleanup();
        }, 200);
    },

    testES2021NumericSeparators: function() {
        console.log('\n=== ES2021: Numeric Separators ===');
        
        // Large numbers with separators for readability
        const million = 1_000_000;
        const billion = 1_000_000_000;
        const trillion = 1_000_000_000_000;
        
        console.log('Million:', million);
        console.log('Billion:', billion);
        console.log('Trillion:', trillion);
        
        // Binary, octal, and hex with separators
        const binary = 0b1010_0001_1000_0101;
        const octal = 0o777_644_321;
        const hex = 0xFF_EC_DE_5E;
        
        console.log('Binary with separators:', binary);
        console.log('Octal with separators:', octal);
        console.log('Hex with separators:', hex);
        
        // Decimal numbers with separators
        const price = 299_99.99;
        const pi = 3.141_592_653_589_793;
        
        console.log('Price:', price);
        console.log('Pi:', pi);
        
        // BigInt with separators
        // const largeBigInt = 123_456_789_012_345_678_901_234_567_890n;
        // console.log('Large BigInt:', largeBigInt);
        
        // Scientific notation with separators
        const scientific = 1_000e-3;
        const largeScientific = 6.022_140_76e23;
        
        console.log('Scientific notation:', scientific);
        console.log('Avogadro number:', largeScientific);
        
        // Practical examples
        const fileSize = 1_073_741_824; // 1 GB in bytes
        const timeout = 5_000; // 5 seconds in milliseconds
        const maxSafeInteger = 9_007_199_254_740_991;
        
        console.log('File size (1GB):', fileSize);
        console.log('Timeout (5s):', timeout);
        console.log('Max safe integer:', maxSafeInteger);
        
        // Note: Separators are only for literal notation, not for parsing
        console.log('typeof 1_000_000:', typeof 1_000_000);
        console.log('1_000_000 === 1000000:', 1_000_000 === 1000000);
    },

    testES2021LogicalAssignment: function() {
        console.log('\n=== ES2021: Logical Assignment Operators ===');
        
        // Logical AND assignment (&&=)
        let obj1 = { name: 'John', age: 30 };
        obj1.name &&= obj1.name.toUpperCase();
        console.log('AND assignment (truthy):', obj1.name);
        
        obj1.email &&= obj1.email.toLowerCase(); // Won't execute
        console.log('AND assignment (falsy):', obj1.email);
        
        // Logical OR assignment (||=)
        let config = { theme: '', timeout: 0, debug: false };
        config.theme ||= 'default';
        config.timeout ||= 5000;
        config.debug ||= true;
        
        console.log('OR assignment config:', config);
        
        // Nullish coalescing assignment (??=)
        let settings = { name: null, count: 0, enabled: false };
        settings.name ??= 'Default Name';
        settings.count ??= 10; // Won't change (0 is not nullish)
        settings.enabled ??= true; // Won't change (false is not nullish)
        settings.newProp ??= 'New Value';
        
        console.log('Nullish assignment settings:', settings);
        
        // Practical examples
        class UserPreferences {
            constructor(options = {}) {
                // Set defaults using logical assignment
                options.theme ??= 'light';
                options.language ??= 'en';
                options.notifications ??= true;
                options.autoSave ??= false;
                
                this.preferences = options;
            }
            
            updatePreference(key, value) {
                // Only update if key exists and value is truthy
                this.preferences[key] &&= value;
            }
            
            setDefault(key, defaultValue) {
                // Set default only if current value is falsy
                this.preferences[key] ||= defaultValue;
            }
            
            setIfNull(key, value) {
                // Set only if current value is null or undefined
                this.preferences[key] ??= value;
            }
        }
        
        const prefs = new UserPreferences({ theme: 'dark', notifications: null });
        console.log('Initial preferences:', prefs.preferences);
        
        prefs.updatePreference('theme', 'blue');
        prefs.setDefault('autoSave', true);
        prefs.setIfNull('notifications', false);
        
        console.log('Updated preferences:', prefs.preferences);
        
        // Comparison with traditional patterns
        let traditional = { value: null };
        
        // Traditional way
        if (traditional.value == null) {
            traditional.value = 'default';
        }
        
        // Modern way
        let modern = { value: null };
        modern.value ??= 'default';
        
        console.log('Traditional vs modern:', traditional.value === modern.value);
    },

    // ========================================
    // ES2022/ES13 FEATURES
    // ========================================

    testES2022Features: function() {
        console.log('\n🚀 === ES2022/ES13 FEATURES ===');
        this.testES2022TopLevelAwait();
        // this.testES2022ClassFields();
        this.testES2022ArrayAt();
        this.testES2022ErrorCause();
        this.testES2022HasOwn();
        this.testES2022RegExpMatchIndices();
    },

    testES2022TopLevelAwait: async function() {
        console.log('\n=== ES2022: Top-level await ===');
        
        // Note: In real modules, you can use await at the top level
        // Here we simulate it within an async function
        
        console.log('Simulating top-level await...');
        
        // Simulated module with top-level await
        const simulateTopLevelAwait = async () => {
            // This would be at the top level in a real module
            const config = await fetch('/api/config').catch(() => ({
                json: () => Promise.resolve({ apiUrl: 'https://api.example.com', timeout: 5000 })
            }));
            
            const configData = await config.json();
            console.log('Config loaded at module level:', configData);
            
            return configData;
        };
        
        const moduleConfig = await simulateTopLevelAwait();
        
        // Dynamic imports with top-level await
        const dynamicModule = {
            async loadUtils() {
                // Simulate dynamic import
                await new Promise(resolve => setTimeout(resolve, 50));
                return {
                    formatDate: (date) => date.toISOString().split('T')[0],
                    capitalize: (str) => str.charAt(0).toUpperCase() + str.slice(1)
                };
            }
        };
        
        const utils = await dynamicModule.loadUtils();
        console.log('Dynamic module loaded:', utils.formatDate(new Date()));
        
        // Multiple parallel top-level awaits
        const [userData, settingsData] = await Promise.all([
            Promise.resolve({ name: 'John', id: 1 }),
            Promise.resolve({ theme: 'dark', lang: 'en' })
        ]);
        
        console.log('Parallel top-level awaits:', { userData, settingsData });
        
        console.log('Benefits: No need for IIFE, cleaner module initialization');
    },

    // testES2022ClassFields: function() {
    //     console.log('\n=== ES2022: Class Fields ===');
        
    //     // Public and private class fields
    //     class BankAccount {
    //         // Public fields
    //         accountType = 'checking';
    //         currency = 'USD';
            
    //         // Private fields
    //         balance = 0;
    //         accountNumber = Math.random().toString(36).substr(2, 9);
    //         pin = '1234';
            
    //         // Static fields
    //         static bankName = 'JS Bank';
    //         static interestRate = 0.02;
            
    //         constructor(initialBalance = 0) {
    //             this.balance = initialBalance;
    //         }
            
    //         // Public methods
    //         deposit(amount) {
    //             if (amount > 0) {
    //                 this.balance += amount;
    //                 return this.balance;
    //             }
    //             throw new Error('Invalid deposit amount');
    //         }
            
    //         withdraw(amount) {
    //             if (amount > 0 && amount <= this.balance) {
    //                 this.balance -= amount;
    //                 return this.balance;
    //             }
    //             throw new Error('Invalid withdrawal amount');
    //         }
            
    //         getBalance() {
    //             return this.balance;
    //         }
            
    //         // Private methods
    //         validatePin(pin) {
    //             return pin === this.pin;
    //         }
            
    //         generateStatement() {
    //             return `Account: ${this.accountNumber}, Balance: ${this.balance}`;
    //         }
            
    //         // Public method using private method
    //         getStatement(pin) {
    //             if (this.validatePin(pin)) {
    //                 return this.generateStatement();
    //             }
    //             throw new Error('Invalid PIN');
    //         }
            
    //         // Static methods
    //         static getInterestRate() {
    //             return this.interestRate;
    //         }
            
    //         static calculateInterest(balance) {
    //             return balance * this.interestRate;
    //         }
            
    //         static calculateYearlyInterest(balance) {
    //             return this.calculateInterest(balance) * 12;
    //         }
    //     }
        
    //     const account = new BankAccount(1000);
    //     console.log('Initial balance:', account.getBalance());
    //     console.log('Account type:', account.accountType);
        
    //     account.deposit(500);
    //     console.log('After deposit:', account.getBalance());
        
    //     account.withdraw(200);
    //     console.log('After withdrawal:', account.getBalance());
        
    //     console.log('Bank name:', BankAccount.bankName);
    //     console.log('Interest rate:', BankAccount.getInterestRate());
    //     console.log('Yearly interest on $1000:', BankAccount.calculateYearlyInterest(1000));
        
    //     // Private fields are truly private
    //     try {
    //         console.log('Trying to access private field:', account.balance);
    //     } catch (error) {
    //         console.log('Cannot access private field:', error.message);
    //     }
        
    //     // Statement with correct PIN
    //     try {
    //         console.log('Statement:', account.getStatement('1234'));
    //     } catch (error) {
    //         console.log('Statement error:', error.message);
    //     }
        
    //     // Statement with incorrect PIN
    //     try {
    //         console.log('Statement with wrong PIN:', account.getStatement('0000'));
    //     } catch (error) {
    //         console.log('Wrong PIN error:', error.message);
    //     }
    // },

    testES2022ArrayAt: function() {
        console.log('\n=== ES2022: Array.at() method ===');
        
        const fruits = ['apple', 'banana', 'cherry', 'date', 'elderberry'];
        
        // Positive indices
        console.log('at(0):', fruits.at(0));
        console.log('at(2):', fruits.at(2));
        
        // Negative indices (from the end)
        console.log('at(-1):', fruits.at(-1)); // Last element
        console.log('at(-2):', fruits.at(-2)); // Second to last
        console.log('at(-5):', fruits.at(-5)); // First element
        
        // Out of bounds
        console.log('at(10):', fruits.at(10)); // undefined
        console.log('at(-10):', fruits.at(-10)); // undefined
        
        // Comparison with bracket notation
        console.log('\nComparison:');
        console.log('fruits[0]:', fruits[0]);
        console.log('fruits.at(0):', fruits.at(0));
        console.log('fruits[fruits.length - 1]:', fruits[fruits.length - 1]);
        console.log('fruits.at(-1):', fruits.at(-1));
        
        // Works with TypedArray and strings too
        const typedArray = new Uint8Array([10, 20, 30, 40, 50]);
        console.log('\nTypedArray.at(-1):', typedArray.at(-1));
        
        const str = 'hello';
        console.log('String.at(-1):', str.at(-1));
        
        // Practical example
        function getLastNElements(array, n) {
            return Array.from({ length: n }, (_, i) => array.at(-(i + 1))).reverse();
        }
        
        console.log('Last 3 fruits:', getLastNElements(fruits, 3));
        
        // Dynamic access
        function getElementFromEnd(array, indexFromEnd) {
            return array.at(-indexFromEnd);
        }
        
        console.log('2nd from end:', getElementFromEnd(fruits, 2));
        console.log('3rd from end:', getElementFromEnd(fruits, 3));
    },

    testES2022ErrorCause: function() {
        console.log('\n=== ES2022: Error.cause ===');
        
        // Basic error with cause
        function parseJSON(jsonString) {
            try {
                return JSON.parse(jsonString);
            } catch (originalError) {
                throw new Error('Failed to parse JSON data', { cause: originalError });
            }
        }
        
        try {
            parseJSON('{ invalid json }');
        } catch (error) {
            console.log('Error message:', error.message);
            console.log('Error cause:', error.cause.message);
            console.log('Original error type:', error.cause.constructor.name);
        }
        
        // Chained errors
        async function fetchUserData(userId) {
            try {
                // Simulate API call
                throw new Error('Network timeout');
            } catch (networkError) {
                throw new Error(`Failed to fetch user ${userId}`, { cause: networkError });
            }
        }
        
        async function getUserProfile(userId) {
            try {
                const userData = await fetchUserData(userId);
                return userData;
            } catch (fetchError) {
                throw new Error('Unable to load user profile', { cause: fetchError });
            }
        }
        
        getUserProfile(123).catch(error => {
            console.log('\nChained errors:');
            console.log('Top level:', error.message);
            console.log('Caused by:', error.cause.message);
            console.log('Root cause:', error.cause.cause.message);
        });
        
        // Custom error with additional context
        class ValidationError extends Error {
            constructor(message, field, value, cause) {
                super(message, { cause });
                this.name = 'ValidationError';
                this.field = field;
                this.value = value;
            }
        }
        
        function validateEmail(email) {
            try {
                if (!email.includes('@')) {
                    throw new Error('Missing @ symbol');
                }
                // More validation...
            } catch (validationError) {
                throw new ValidationError(
                    'Email validation failed',
                    'email',
                    email,
                    validationError
                );
            }
        }
        
        try {
            validateEmail('invalid-email');
        } catch (error) {
            console.log('\nCustom error with cause:');
            console.log('Error type:', error.constructor.name);
            console.log('Message:', error.message);
            console.log('Field:', error.field);
            console.log('Value:', error.value);
            console.log('Root cause:', error.cause.message);
        }
        
        // Error cause with structured data
        function processOrder(order) {
            try {
                // Simulate processing
                throw new Error('Payment declined');
            } catch (paymentError) {
                throw new Error('Order processing failed', {
                    cause: {
                        originalError: paymentError,
                        orderId: order.id,
                        timestamp: new Date(),
                        retryable: true
                    }
                });
            }
        }
        
        try {
            processOrder({ id: 'ORD-123', amount: 99.99 });
        } catch (error) {
            console.log('\nStructured error cause:');
            console.log('Error:', error.message);
            console.log('Order ID:', error.cause.orderId);
            console.log('Retryable:', error.cause.retryable);
            console.log('Original:', error.cause.originalError.message);
        }
    },

    testES2022HasOwn: function() {
        console.log('\n=== ES2022: Object.hasOwn() ===');
        
        const obj = {
            name: 'JavaScript',
            version: 'ES2022'
        };
        
        // Basic usage
        console.log('Object.hasOwn(obj, "name"):', Object.hasOwn(obj, 'name'));
        console.log('Object.hasOwn(obj, "age"):', Object.hasOwn(obj, 'age'));
        
        // Inherited properties
        const parent = { age: 25 };
        const child = Object.create(parent);
        child.name = 'John';
        
        console.log('Object.hasOwn(child, "name"):', Object.hasOwn(child, 'name'));
        console.log('Object.hasOwn(child, "age"):', Object.hasOwn(child, 'age'));
        
        // Own properties vs. prototype
        console.log('Object.hasOwn(obj, "toString"):', Object.hasOwn(obj, 'toString'));
        console.log('Object.hasOwn(child, "toString"):', Object.hasOwn(child, 'toString'));
    },

    // ========================================
    // ES2023/ES14 FEATURE IMPLEMENTATIONS
    // ========================================

    testES2023Features: function() {
        console.log('\n🚀 === ES2023/ES14 FEATURES ===');
        this.testES2023FindLast();
        this.testES2023HashbangSyntax();
        this.testES2023SymbolsAsWeakMapKeys();
        this.testES2023ChangeArrayByCopy();
    },

    testES2023FindLast: function() {
        console.log('\n=== ES2023: Array findLast & findLastIndex ===');
        
        const numbers = [1, 5, 10, 15, 20, 25, 30];
        const people = [
            { name: 'Alice', age: 25, active: true },
            { name: 'Bob', age: 30, active: false },
            { name: 'Charlie', age: 35, active: true },
            { name: 'David', age: 40, active: true },
            { name: 'Eve', age: 28, active: false }
        ];
        
        // findLast - finds the last element matching condition
        const lastLargeNumber = numbers.findLast ? numbers.findLast(num => num > 15) : 
            [...numbers].reverse().find(num => num > 15);
        console.log('Last number > 15:', lastLargeNumber);
        
        const lastActivePerson = people.findLast ? people.findLast(person => person.active) :
            [...people].reverse().find(person => person.active);
        console.log('Last active person:', lastActivePerson?.name);
        
        // findLastIndex - finds the index of last matching element
        const lastLargeIndex = numbers.findLastIndex ? numbers.findLastIndex(num => num > 15) :
            numbers.length - 1 - [...numbers].reverse().findIndex(num => num > 15);
        console.log('Index of last number > 15:', lastLargeIndex);
        
        const lastActiveIndex = people.findLastIndex ? people.findLastIndex(person => person.active) :
            people.length - 1 - [...people].reverse().findIndex(person => person.active);
        console.log('Index of last active person:', lastActiveIndex);
        
        // Comparison with find and findIndex
        console.log('\nComparison:');
        console.log('find (first > 15):', numbers.find(num => num > 15));
        console.log('findLast (last > 15):', lastLargeNumber);
        console.log('findIndex (first > 15):', numbers.findIndex(num => num > 15));
        console.log('findLastIndex (last > 15):', lastLargeIndex);
        
        // When no match is found
        const notFound = numbers.findLast ? numbers.findLast(num => num > 100) : undefined;
        const notFoundIndex = numbers.findLastIndex ? numbers.findLastIndex(num => num > 100) : -1;
        console.log('\nNot found cases:');
        console.log('findLast (> 100):', notFound); // undefined
        console.log('findLastIndex (> 100):', notFoundIndex); // -1
        
        // Practical examples
        const transactions = [
            { id: 1, type: 'deposit', amount: 100, date: '2024-01-01' },
            { id: 2, type: 'withdrawal', amount: 50, date: '2024-01-02' },
            { id: 3, type: 'deposit', amount: 200, date: '2024-01-03' },
            { id: 4, type: 'withdrawal', amount: 30, date: '2024-01-04' },
            { id: 5, type: 'deposit', amount: 150, date: '2024-01-05' }
        ];
        
        // Find last deposit
        const lastDeposit = transactions.findLast ? 
            transactions.findLast(t => t.type === 'deposit') :
            [...transactions].reverse().find(t => t.type === 'deposit');
        console.log('\nLast deposit:', lastDeposit);
        
        // Find last large transaction
        const lastLargeTransaction = transactions.findLast ?
            transactions.findLast(t => t.amount >= 100) :
            [...transactions].reverse().find(t => t.amount >= 100);
        console.log('Last large transaction:', lastLargeTransaction);
    },

    // testES2023HashbangSyntax: function() {
    //     console.log('\n=== ES2023: Hashbang (#!) Syntax ===');
        
    //     // Note: Hashbang syntax is primarily for executable JavaScript files
    //     // It allows JS files to be run directly as scripts in Unix-like systems
        
    //     console.log('Hashbang syntax example:');
    //     console.log('#!/usr/bin/env node');
    //     console.log('console.log("Hello from executable JS file");');
        
    //     // The hashbang must be the very first line of the file
    //     // It's ignored by JavaScript engines but used by the shell
        
    //     console.log('\nCommon hashbangs:');
    //     console.log('Node.js: #!/usr/bin/env node');
    //     console.log('Deno: #!/usr/bin/env deno run');
    //     console.log('Bun: #!/usr/bin/env bun');
        
    //     // Example of how it would be used:
    //     const scriptContent = `#!/usr/bin/env node
    // // This file can be executed directly: ./script.js
    // console.log('Running JavaScript as executable script');
    // process.exit(0);`;
        
    //     console.log('\nExample executable script:');
    //     console.log(scriptContent);
        
    //     // Benefits:
    //     console.log('\nBenefits of hashbang syntax:');
    //     console.log('- Make JS files directly executable');
    //     console.log('- No need to type "node script.js"');
    //     console.log('- Can run "./script.js" directly');
    //     console.log('- Standardizes executable JS files');
        
    //     // File permissions example (conceptual)
    //     console.log('\nTo make executable (Unix/Linux):');
    //     console.log('chmod +x script.js');
    //     console.log('./script.js');
        
    //     // Multiple runtime support
    //     console.log('\nCan specify different JavaScript runtimes:');
    //     const examples = [
    //         '#!/usr/bin/env node',
    //         '#!/usr/bin/env deno run --allow-all',
    //         '#!/usr/bin/env bun run',
    //         '#!/usr/local/bin/node'
    //     ];
        
    //     examples.forEach(example => console.log(example));
    // },

    // testES2023SymbolsAsWeakMapKeys: function() {
    //     console.log('\n=== ES2023: Symbols as WeakMap Keys ===');
        
    //     // Previously, WeakMap only accepted objects as keys
    //     // ES2023 allows symbols as WeakMap keys
        
    //     const weakMap = new WeakMap();
        
    //     // Symbol keys
    //     const sym1 = Symbol('key1');
    //     const sym2 = Symbol('key2');
    //     const sym3 = Symbol.for('global-key');
        
    //     // Set values with symbol keys
    //     try {
    //         weakMap.set(sym1, 'Value for sym1');
    //         weakMap.set(sym2, { data: 'Complex value for sym2' });
    //         weakMap.set(sym3, ['array', 'value', 'for', 'global', 'symbol']);
            
    //         console.log('WeakMap with symbol keys:');
    //         console.log('sym1 value:', weakMap.get(sym1));
    //         console.log('sym2 value:', weakMap.get(sym2));
    //         console.log('sym3 value:', weakMap.get(sym3));
            
    //         // Check if symbols exist
    //         console.log('Has sym1:', weakMap.has(sym1));
    //         console.log('Has sym2:', weakMap.has(sym2));
    //     } catch (error) {
    //         console.log('Symbols as WeakMap keys not supported in this environment');
    //         console.log('Using alternative Map for demonstration:');
            
    //         const alternativeMap = new Map();
    //         alternativeMap.set(sym1, 'Value for sym1');
    //         alternativeMap.set(sym2, { data: 'Complex value for sym2' });
    //         console.log('sym1 value (Map):', alternativeMap.get(sym1));
    //         console.log('sym2 value (Map):', alternativeMap.get(sym2));
    //     }
        
    //     // Object keys still work
    //     const objKey = { id: 'object-key' };
    //     weakMap.set(objKey, 'Value for object key');
    //     console.log('Object key value:', weakMap.get(objKey));
        
    //     // Practical example - private data storage
    //     class SecureData {
    //         static #privateDataMap = new WeakMap();
    //         static #accessTokens = new Map();
            
    //         constructor(data) {
    //             const accessToken = Symbol('access-token');
    //             const privateSymbol = Symbol('private-data');
                
    //             // Store private data using symbol key (if supported)
    //             try {
    //                 SecureData.#privateDataMap.set(privateSymbol, data);
    //                 SecureData.#accessTokens.set(this, { accessToken, privateSymbol });
    //             } catch (error) {
    //                 // Fallback to object key
    //                 const privateKey = { instance: this };
    //                 SecureData.#privateDataMap.set(privateKey, data);
    //                 SecureData.#accessTokens.set(this, { accessToken, privateKey });
    //             }
    //         }
            
    //         getData() {
    //             const tokens = SecureData.#accessTokens.get(this);
    //             if (tokens) {
    //                 const key = tokens.privateSymbol || tokens.privateKey;
    //                 return SecureData.#privateDataMap.get(key);
    //             }
    //             throw new Error('Access denied');
    //         }
            
    //         destroy() {
    //             const tokens = SecureData.#accessTokens.get(this);
    //             if (tokens) {
    //                 const key = tokens.privateSymbol || tokens.privateKey;
    //                 SecureData.#privateDataMap.delete(key);
    //                 SecureData.#accessTokens.delete(this);
    //             }
    //         }
    //     }
        
    //     const secureInstance = new SecureData({ secret: 'top-secret-data', id: 12345 });
    //     console.log('\nSecure data access:', secureInstance.getData());
        
    //     // Benefits of symbols as WeakMap keys
    //     console.log('\nBenefits:');
    //     console.log('- Symbols provide unique, collision-free keys');
    //     console.log('- Automatic cleanup when symbols are garbage collected');
    //     console.log('- Enhanced privacy and encapsulation');
    //     console.log('- Metadata can be attached without affecting objects');
    // },

    testES2023ChangeArrayByCopy: function() {
        console.log('\n=== ES2023: Change Array by Copy ===');
        
        const original = [1, 2, 3, 4, 5];
        console.log('Original array:', original);
        
        // toReversed() - returns reversed copy
        const reversed = original.toReversed ? original.toReversed() : [...original].reverse();
        console.log('toReversed():', reversed);
        console.log('Original unchanged:', original);
        
        // toSorted() - returns sorted copy
        const numbers = [3, 1, 4, 1, 5, 9, 2, 6];
        const sorted = numbers.toSorted ? numbers.toSorted() : [...numbers].sort();
        const sortedDesc = numbers.toSorted ? 
            numbers.toSorted((a, b) => b - a) : 
            [...numbers].sort((a, b) => b - a);
        
        console.log('\nOriginal numbers:', numbers);
        console.log('toSorted():', sorted);
        console.log('toSorted(desc):', sortedDesc);
        console.log('Original unchanged:', numbers);
        
        // toSpliced() - returns spliced copy
        const fruits = ['apple', 'banana', 'cherry', 'date'];
        
        // Polyfill for toSpliced if not available
        const toSpliced = (arr, start, deleteCount, ...items) => {
            if (arr.toSpliced) {
                return arr.toSpliced(start, deleteCount, ...items);
            }
            const copy = [...arr];
            copy.splice(start, deleteCount, ...items);
            return copy;
        };
        
        const spliced1 = toSpliced(fruits, 1, 2); // Remove 2 elements starting at index 1
        const spliced2 = toSpliced(fruits, 1, 1, 'blueberry', 'coconut'); // Replace 1, add 2
        const spliced3 = toSpliced(fruits, 2, 0, 'elderberry'); // Insert at index 2
        
        console.log('\nOriginal fruits:', fruits);
        console.log('toSpliced(1, 2):', spliced1);
        console.log('toSpliced(1, 1, ...new):', spliced2);
        console.log('toSpliced(2, 0, "elderberry"):', spliced3);
        console.log('Original unchanged:', fruits);
        
        // with() - returns copy with element replaced at index
        const colors = ['red', 'green', 'blue', 'yellow'];
        
        // Polyfill for with if not available
        const withReplacement = (arr, index, value) => {
            if (arr.with) {
                return arr.with(index, value);
            }
            const copy = [...arr];
            const actualIndex = index < 0 ? arr.length + index : index;
            if (actualIndex >= 0 && actualIndex < arr.length) {
                copy[actualIndex] = value;
            }
            return copy;
        };
        
        const withNew = withReplacement(colors, 1, 'purple');
        const withNegativeIndex = withReplacement(colors, -1, 'orange'); // Negative indices work
        
        console.log('\nOriginal colors:', colors);
        console.log('with(1, "purple"):', withNew);
        console.log('with(-1, "orange"):', withNegativeIndex);
        console.log('Original unchanged:', colors);
        
        // Comparison with mutating methods
        console.log('\nComparison - Mutating vs Non-mutating:');
        
        const mutableArray = [1, 2, 3, 4, 5];
        const immutableArray = [1, 2, 3, 4, 5];
        
        // Mutating methods
        console.log('Before mutation:', mutableArray);
        mutableArray.reverse();
        console.log('After reverse():', mutableArray);
        mutableArray.sort();
        console.log('After sort():', mutableArray);
        
        // Non-mutating methods
        console.log('Immutable original:', immutableArray);
        const newReversed = immutableArray.toReversed ? immutableArray.toReversed() : [...immutableArray].reverse();
        const newSorted = immutableArray.toSorted ? immutableArray.toSorted() : [...immutableArray].sort();
        console.log('toReversed() result:', newReversed);
        console.log('toSorted() result:', newSorted);
        console.log('Original still unchanged:', immutableArray);
        
        // Chaining operations
        const data = [64, 34, 25, 12, 22, 11, 90];
        let processed = data.toSorted ? data.toSorted((a, b) => a - b) : [...data].sort((a, b) => a - b);
        processed = processed.toReversed ? processed.toReversed() : [...processed].reverse();
        processed = withReplacement(processed, 0, 100);
        processed = toSpliced(processed, 1, 2);
        
        console.log('\nChaining operations:');
        console.log('Original data:', data);
        console.log('Processed:', processed);
        console.log('Original unchanged:', data);
        
        // Practical example - state management
        class ImmutableList {
            constructor(items = []) {
                this.items = [...items];
            }
            
            add(item) {
                return new ImmutableList([...this.items, item]);
            }
            
            remove(index) {
                return new ImmutableList(toSpliced(this.items, index, 1));
            }
            
            update(index, newValue) {
                return new ImmutableList(withReplacement(this.items, index, newValue));
            }
            
            sort(compareFn) {
                const sorted = this.items.toSorted ? 
                    this.items.toSorted(compareFn) : 
                    [...this.items].sort(compareFn);
                return new ImmutableList(sorted);
            }
            
            reverse() {
                const reversed = this.items.toReversed ? 
                    this.items.toReversed() : 
                    [...this.items].reverse();
                return new ImmutableList(reversed);
            }
            
            toArray() {
                return [...this.items];
            }
        }
        
        const list = new ImmutableList(['a', 'b', 'c']);
        const newList = list
            .add('d')
            .update(1, 'B')
            .sort()
            .reverse();
        
        console.log('\nImmutable list example:');
        console.log('Original list:', list.toArray());
        console.log('Modified list:', newList.toArray());
    },

    // ========================================
    // ES2024/ES15 FEATURE IMPLEMENTATIONS
    // ========================================

    testES2024Features: function() {
        console.log('\n🚀 === ES2024/ES15 FEATURES ===');
        this.testES2024GroupBy();
        this.testES2024WellFormedUnicode();
        this.testES2024PromiseWithResolvers();
        this.testES2024RegExpVFlag();
    },

    testES2024GroupBy: function() {
        console.log('\n=== ES2024: Object.groupBy & Map.groupBy ===');
        
        // Sample data
        const people = [
            { name: 'Alice', age: 25, department: 'Engineering', salary: 80000 },
            { name: 'Bob', age: 30, department: 'Engineering', salary: 90000 },
            { name: 'Charlie', age: 35, department: 'Sales', salary: 70000 },
            { name: 'David', age: 28, department: 'Sales', salary: 65000 },
            { name: 'Eve', age: 32, department: 'Marketing', salary: 75000 },
            { name: 'Frank', age: 29, department: 'Engineering', salary: 85000 }
        ];
        
        // Polyfill for Object.groupBy
        const objectGroupBy = (array, keyFn) => {
            if (Object.groupBy) {
                return Object.groupBy(array, keyFn);
            }
            return array.reduce((groups, item) => {
                const key = keyFn(item);
                if (!groups[key]) {
                    groups[key] = [];
                }
                groups[key].push(item);
                return groups;
            }, {});
        };
        
        // Polyfill for Map.groupBy
        const mapGroupBy = (array, keyFn) => {
            if (Map.groupBy) {
                return Map.groupBy(array, keyFn);
            }
            const map = new Map();
            for (const item of array) {
                const key = keyFn(item);
                if (!map.has(key)) {
                    map.set(key, []);
                }
                map.get(key).push(item);
            }
            return map;
        };
        
        // Object.groupBy - groups into a plain object
        const byDepartment = objectGroupBy(people, person => person.department);
        console.log('Grouped by department:');
        Object.entries(byDepartment).forEach(([dept, group]) => {
            console.log(`${dept}: ${group.length} people`);
        });
        
        const byAgeGroup = objectGroupBy(people, person => {
            if (person.age < 30) return 'young';
            if (person.age < 35) return 'middle';
            return 'senior';
        });
        console.log('\nGrouped by age group:');
        Object.entries(byAgeGroup).forEach(([group, people]) => {
            console.log(`${group}: ${people.map(p => p.name).join(', ')}`);
        });
        
        // Map.groupBy - groups into a Map (useful for complex keys)
        const bySalaryRange = mapGroupBy(people, person => {
            const range = Math.floor(person.salary / 10000) * 10000;
            return `${range}-${range + 9999}`;
        });
        
        console.log('\nGrouped by salary range (Map):');
        for (const [range, group] of bySalaryRange) {
            console.log(`${range}: ${group.length} people - ${group.map(p => p.name).join(', ')}`);
        }
        
        // Complex grouping examples
        const sales = [
            { product: 'Laptop', category: 'Electronics', quarter: 'Q1', amount: 5000 },
            { product: 'Phone', category: 'Electronics', quarter: 'Q1', amount: 3000 },
            { product: 'Desk', category: 'Furniture', quarter: 'Q1', amount: 1000 },
            { product: 'Laptop', category: 'Electronics', quarter: 'Q2', amount: 5500 },
            { product: 'Chair', category: 'Furniture', quarter: 'Q2', amount: 800 },
        ];
        
        // Group by multiple criteria
        const multiGroupBy = (array, ...keyFns) => {
            return objectGroupBy(array, item => keyFns.map(fn => fn(item)).join('-'));
        };
        
        const byCategoryAndQuarter = multiGroupBy(
            sales,
            sale => sale.category,
            sale => sale.quarter
        );
        
        console.log('\nGrouped by category and quarter:');
        Object.entries(byCategoryAndQuarter).forEach(([key, items]) => {
            const totalAmount = items.reduce((sum, item) => sum + item.amount, 0);
            console.log(`${key}: $${totalAmount} (${items.length} items)`);
        });
        
        // Aggregation after grouping
        const departmentStats = Object.entries(byDepartment).map(([dept, group]) => ({
            department: dept,
            count: group.length,
            avgSalary: Math.round(group.reduce((sum, p) => sum + p.salary, 0) / group.length),
            avgAge: Math.round(group.reduce((sum, p) => sum + p.age, 0) / group.length),
            totalSalary: group.reduce((sum, p) => sum + p.salary, 0)
        }));
        
        console.log('\nDepartment statistics:');
        departmentStats.forEach(stat => {
            console.log(`${stat.department}: ${stat.count} people, avg salary $${stat.avgSalary}, avg age ${stat.avgAge}`);
        });
    },

    testES2024WellFormedUnicode: function() {
        console.log('\n=== ES2024: Well-formed Unicode Strings ===');
        
        // String.isWellFormed() - checks if string contains well-formed Unicode
        const wellFormed = 'Hello 🌍 World';
        const illFormed = 'Hello \uD800 World'; // Lone surrogate
        
        // Polyfill for isWellFormed
        const isWellFormed = (str) => {
            if (str.isWellFormed) {
                return str.isWellFormed();
            }
            // Simple check for lone surrogates
            try {
                encodeURIComponent(str);
                return true;
            } catch (e) {
                return false;
            }
        };
        
        // Polyfill for toWellFormed
        const toWellFormed = (str) => {
            if (str.toWellFormed) {
                return str.toWellFormed();
            }
            // Replace lone surrogates with replacement character
            return str.replace(/[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?<![\uD800-\uDBFF])[\uDC00-\uDFFF]/g, '\uFFFD');
        };
        
        console.log('Well-formed string test:', isWellFormed(wellFormed));
        console.log('Ill-formed string test:', isWellFormed(illFormed));
        
        // String.toWellFormed() - returns well-formed version
        const corrected = toWellFormed(illFormed);
        console.log('Original ill-formed:', illFormed);
        console.log('Corrected:', corrected);
        console.log('Corrected is well-formed:', isWellFormed(corrected));
        
        // Examples with different Unicode issues
        const testStrings = [
            'Normal ASCII text',
            'Text with emoji 🎉',
            'Text with accents: café, naïve',
            'High surrogate only: \uD83D',
            'Low surrogate only: \uDE00',
            'Proper emoji: \uD83D\uDE00', // 😀
            'Mixed: Hello \uD800 World \uD83D\uDE00'
        ];
        
        console.log('\nTesting various strings:');
        testStrings.forEach((str, index) => {
            const wellFormedStatus = isWellFormed(str);
            const correctedStr = toWellFormed(str);
            console.log(`${index + 1}. "${str}"`);
            console.log(`   Well-formed: ${wellFormedStatus}`);
            if (!wellFormedStatus) {
                console.log(`   Corrected: "${correctedStr}"`);
            }
        });
        
        // Practical usage - safe string processing
        function safeStringProcess(input) {
            console.log('\nSafe string processing:');
            console.log('Input:', input);
            console.log('Is well-formed:', isWellFormed(input));
            
            // Ensure string is well-formed before processing
            const safeInput = toWellFormed(input);
            console.log('Safe version:', safeInput);
            
            // Now safe to use with APIs that require well-formed Unicode
            try {
                const encoded = encodeURIComponent(safeInput);
                console.log('URL encoded:', encoded);
                
                const jsonString = JSON.stringify(safeInput);
                console.log('JSON encoded:', jsonString);
                
                return safeInput;
            } catch (error) {
                console.log('Processing error:', error.message);
                return null;
            }
        }
        
        safeStringProcess('Hello \uD800 World'); // Ill-formed
        safeStringProcess('Hello 🌍 World'); // Well-formed
        
        // Unicode normalization combined with well-formed check
        function normalizeAndValidate(str) {
            const wellFormed = toWellFormed(str);
            const normalized = wellFormed.normalize('NFC'); // Canonical decomposition, then canonical composition
            
            return {
                original: str,
                wellFormed: wellFormed,
                normalized: normalized,
                isWellFormed: isWellFormed(str),
                length: str.length,
                normalizedLength: normalized.length
            };
        }
        
        const testString = 'café \uD800 naïve'; // Contains ill-formed surrogate
        const result = normalizeAndValidate(testString);
        
        console.log('\nNormalization and validation:');
        Object.entries(result).forEach(([key, value]) => {
            console.log(`${key}: ${typeof value === 'string' ? `"${value}"` : value}`);
        });
    },

    testES2024PromiseWithResolvers: function() {
        console.log('\n=== ES2024: Promise.withResolvers ===');
        
        // Polyfill for Promise.withResolvers
        const promiseWithResolvers = () => {
            if (Promise.withResolvers) {
                return Promise.withResolvers();
            }
            
            let resolve, reject;
            const promise = new Promise((res, rej) => {
                resolve = res;
                reject = rej;
            });
            
            return { promise, resolve, reject };
        };
        
        // Basic usage
        console.log('Creating promise with external resolvers...');
        const { promise: basicPromise, resolve: basicResolve, reject: basicReject } = promiseWithResolvers();
        
        // Resolve after 1 second
        setTimeout(() => {
            basicResolve('Promise resolved externally!');
        }, 100);
        
        basicPromise.then(result => {
            console.log('Basic result:', result);
        });
        
        // Practical example - Manual promise control
        class AsyncQueue {
            constructor() {
                this.queue = [];
                this.processing = false;
            }
            
            async add(task) {
                const { promise, resolve, reject } = promiseWithResolvers();
                
                this.queue.push({
                    task,
                    resolve,
                    reject
                });
                
                if (!this.processing) {
                    this.processQueue();
                }
                
                return promise;
            }
            
            async processQueue() {
                this.processing = true;
                
                while (this.queue.length > 0) {
                    const { task, resolve, reject } = this.queue.shift();
                    
                    try {
                        console.log('Processing task...');
                        const result = await task();
                        resolve(result);
                    } catch (error) {
                        reject(error);
                    }
                    
                    // Small delay between tasks
                    await new Promise(r => setTimeout(r, 50));
                }
                
                this.processing = false;
            }
        }
        
        const queue = new AsyncQueue();
        
        // Add some tasks
        console.log('\nTesting AsyncQueue:');
        queue.add(() => 'Task 1 completed').then(result => console.log(result));
        queue.add(() => 'Task 2 completed').then(result => console.log(result));
        queue.add(() => { throw new Error('Task 3 failed'); }).catch(error => console.log('Error:', error.message));
        queue.add(() => 'Task 4 completed').then(result => console.log(result));
        
        // Event-driven promise resolution
        class EventPromise {
            constructor() {
                this.promises = new Map();
            }
            
            waitFor(eventName) {
                if (this.promises.has(eventName)) {
                    return this.promises.get(eventName).promise;
                }
                
                const { promise, resolve, reject } = promiseWithResolvers();
                this.promises.set(eventName, { promise, resolve, reject });
                
                return promise;
            }
            
            emit(eventName, data) {
                const promiseData = this.promises.get(eventName);
                if (promiseData) {
                    promiseData.resolve(data);
                    this.promises.delete(eventName);
                }
            }
            
            emitError(eventName, error) {
                const promiseData = this.promises.get(eventName);
                if (promiseData) {
                    promiseData.reject(error);
                    this.promises.delete(eventName);
                }
            }
        }
        
        const eventPromise = new EventPromise();
        
        // Wait for events
        console.log('\nTesting EventPromise:');
        eventPromise.waitFor('userLogin').then(user => {
            console.log('User logged in:', user.name);
        });
        
        eventPromise.waitFor('dataLoaded').then(data => {
            console.log('Data loaded:', data.length, 'items');
        });
        
        eventPromise.waitFor('errorEvent').catch(error => {
            console.log('Error occurred:', error.message);
        });
        
        // Simulate events
        setTimeout(() => {
            eventPromise.emit('userLogin', { name: 'Alice', id: 1 });
        }, 200);
        
        setTimeout(() => {
            eventPromise.emit('dataLoaded', [1, 2, 3, 4, 5]);
        }, 300);
        
        setTimeout(() => {
            eventPromise.emitError('errorEvent', new Error('Something went wrong'));
        }, 400);
        
        // Timeout wrapper using withResolvers
        function withTimeout(promise, timeoutMs) {
            const { promise: timeoutPromise, reject: timeoutReject } = promiseWithResolvers();
            
            const timeoutId = setTimeout(() => {
                timeoutReject(new Error(`Operation timed out after ${timeoutMs}ms`));
            }, timeoutMs);
            
            return Promise.race([
                promise.finally(() => clearTimeout(timeoutId)),
                timeoutPromise
            ]);
        }
        
        // Test timeout functionality
        setTimeout(async () => {
            console.log('\nTesting timeout wrapper:');
            
            try {
                const fastPromise = new Promise(resolve => setTimeout(() => resolve('Fast!'), 100));
                const result = await withTimeout(fastPromise, 200);
                console.log('Fast promise result:', result);
            } catch (error) {
                console.log('Fast promise error:', error.message);
            }
            
            try {
                const slowPromise = new Promise(resolve => setTimeout(() => resolve('Slow!'), 300));
                const result = await withTimeout(slowPromise, 200);
                console.log('Slow promise result:', result);
            } catch (error) {
                console.log('Slow promise error:', error.message);
            }
        }, 500);
        
        // Deferred execution pattern
        function createDeferred() {
            const { promise, resolve, reject } = promiseWithResolvers();
            
            return {
                promise,
                resolve,
                reject,
                isSettled: false,
                onSettle: () => {
                    promise.finally(() => {
                        this.isSettled = true;
                    });
                }
            };
        }
        
        const deferred = createDeferred();
        deferred.onSettle();
        
        console.log('\nDeferred execution:');
        console.log('Deferred is settled:', deferred.isSettled);
        
        deferred.promise.then(value => {
            console.log('Deferred resolved with:', value);
            console.log('Deferred is now settled:', deferred.isSettled);
        });
        
        setTimeout(() => {
            deferred.resolve('Deferred value');
        }, 600);
    },

    // testES2024RegExpVFlag: function() {
    //     console.log('\n=== ES2024: RegExp v Flag ===');
        
    //     // The 'v' flag enables set notation and string properties in character classes
    //     // Note: This is a very new feature and may not be supported in all environments
        
    //     console.log('RegExp v flag enables enhanced Unicode property escapes');
    //     console.log('and set notation in character classes.\n');
        
    //     // Basic example (may not work in all environments)
    //     try {
    //         // Set notation - union of character sets
    //         const unionPattern = /[\p{ASCII}&&\p{Letter}]/v;
    //         console.log('Union pattern created successfully');
            
    //         const testString = 'Hello123 Мир';
    //         const matches = testString.match(unionPattern);
    //         console.log('Union pattern matches:', matches);
    //     } catch (error) {
    //         console.log('v flag not supported, demonstrating concepts with traditional regex');
    //     }
        
    //     // Demonstrate enhanced Unicode properties (conceptual)
    //     console.log('Enhanced Unicode properties with v flag:');
        
    //     // Traditional Unicode property escapes (work without v flag)
    //     const emojiPattern = /\p{Emoji}/gu;
    //     const scriptPattern = /\p{Script=Latin}/gu;
    //     const categoryPattern = /\p{Letter}/gu;
        
    //     const testText = 'Hello 🌍 World! Привет мир! 123';
        
    //     console.log('Text:', testText);
    //     console.log('Emoji matches:', testText.match(emojiPattern) || []);
    //     console.log('Latin script matches:', testText.match(scriptPattern) || []);
    //     console.log('Letter matches:', testText.match(categoryPattern) || []);
        
    //     // Set operations (conceptual - what v flag enables)
    //     console.log('\nSet operations enabled by v flag:');
    //     console.log('Union: [A--Z] (characters A through Z)');
    //     console.log('Intersection: [\\p{ASCII}&&\\p{Letter}] (ASCII letters only)');
    //     console.log('Subtraction: [\\p{Letter}--\\p{ASCII}] (non-ASCII letters)');
    //     console.log('Symmetric difference: [\\p{Letter}~~\\p{ASCII}] (letters XOR ASCII)');
        
    //     // Demonstrate with polyfill/workaround patterns
    //     const createCharacterSetTests = () => {
    //         const tests = [
    //             {
    //                 name: 'ASCII Letters',
    //                 pattern: /[A-Za-z]/g,
    //                 description: 'Traditional ASCII letter matching'
    //             },
    //             {
    //                 name: 'Unicode Letters',
    //                 pattern: /\p{Letter}/gu,
    //                 description: 'All Unicode letters'
    //             },
    //             {
    //                 name: 'Digits',
    //                 pattern: /\p{Number}/gu,
    //                 description: 'All Unicode numbers'
    //             },
    //             {
    //                 name: 'Punctuation',
    //                 pattern: /\p{Punctuation}/gu,
    //                 description: 'All Unicode punctuation'
    //             },
    //             {
    //                 name: 'Emoji',
    //                 pattern: /\p{Emoji}/gu,
    //                 description: 'Unicode emoji characters'
    //             }
    //         ];
            
    //         const sampleText = 'Hello! 123 🌟 Café naïve Москва 東京 ♪♫♪';
            
    //         console.log('\nCharacter set matching examples:');
    //         console.log('Sample text:', sampleText);
            
    //         tests.forEach(test => {
    //             const matches = sampleText.match(test.pattern) || [];
    //             console.log(`${test.name}: [${matches.join(', ')}] - ${test.description}`);
    //         });
    //     };
        
    //     createCharacterSetTests();
        
    //     // String properties in character classes (v flag feature)
    //     console.log('\nString properties (v flag feature):');
    //     console.log('\\p{RGI_Emoji} - Recommended for General Interchange emoji');
    //     console.log('\\p{ASCII_Hex_Digit} - ASCII hexadecimal digits');
    //     console.log('\\p{Bidi_Control} - Bidirectional control characters');
        
    //     // Practical examples of what v flag enables
    //     const practicalExamples = () => {
    //         console.log('\nPractical v flag applications:');
            
    //         // Example 1: Validating specific character sets
    //         console.log('1. Enhanced validation patterns:');
    //         console.log('   Username: [\\p{Letter}\\p{Number}_-] (letters, numbers, underscore, dash)');
    //         console.log('   Password: [\\p{Letter}\\p{Number}\\p{Symbol}] (letters, numbers, symbols)');
            
    //         // Example 2: Text processing
    //         console.log('2. Advanced text processing:');
    //         console.log('   Remove non-printable: [^\\p{Print}] replacement');
    //         console.log('   Extract identifiers: [\\p{ID_Start}][\\p{ID_Continue}]*');
            
    //         // Example 3: Internationalization
    //         console.log('3. Internationalization support:');
    //         console.log('   Multi-script text: [\\p{Script=Latin}\\p{Script=Cyrillic}]');
    //         console.log('   Currency symbols: \\p{Currency_Symbol}');
            
    //         // Simulated validation functions
    //         const validateUsername = (username) => {
    //             // Simulating v flag behavior with traditional regex
    //             const pattern = /^[a-zA-Z0-9_-]+$/;
    //             return pattern.test(username);
    //         };
            
    //         const extractWords = (text) => {
    //             // Simulating enhanced word extraction
    //             const pattern = /\b\p{Letter}+\b/gu;
    //             return text.match(pattern) || [];
    //         };
            
    //         console.log('\nValidation examples:');
    //         const usernames = ['user123', 'user_name', 'user-name', 'user@name', 'имя_пользователя'];
    //         usernames.forEach(username => {
    //             console.log(`"${username}": ${validateUsername(username) ? 'valid' : 'invalid'}`);
    //         });
            
    //         console.log('\nWord extraction:');
    //         const multilingualText = 'Hello world! Привет мир! こんにちは世界！';
    //         const words = extractWords(multilingualText);
    //         console.log('Extracted words:', words);
    //     };
        
    //     practicalExamples();
        
    //     // Benefits of v flag
    //     console.log('\nBenefits of RegExp v flag:');
    //     console.log('✓ More powerful character class operations');
    //     console.log('✓ Better Unicode support');
    //     console.log('✓ Set-based character matching');
    //     console.log('✓ Enhanced internationalization capabilities');
    //     console.log('✓ More precise pattern matching');
    //     console.log('✓ Improved readability for complex patterns');
        
    //     // Browser support note
    //     console.log('\nNote: RegExp v flag is cutting-edge and requires very recent browser versions.');
    //     console.log('Always check browser compatibility before using in production.');
    // }
});